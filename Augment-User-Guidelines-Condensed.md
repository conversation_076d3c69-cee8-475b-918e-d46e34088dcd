# 🚀 DassoShu Reader - Flutter Development Guidelines (Essential)

## 📋 Project Overview
**DassoShu Reader**: Professional Chinese language learning e-book reader
- **Flutter SDK**: `>=3.5.2 <4.0.0`
- **Material Design**: Material 3 with `useMaterial3: true`
- **State Management**: Flutter Riverpod 2.5.1+ with code generation
- **Chinese Support**: `chinese_font_library` for proper font rendering

## 🎯 **CORE PRINCIPLES** (ALWAYS FOLLOW)

### **1. 🚫 NEVER BREAK EXISTING FUNCTIONALITY**
- Preserve everything - maintain all existing features and behaviors
- Respect existing architecture - work within established patterns
- Maintain backward compatibility - ensure smooth upgrades
- Test thoroughly before implementing changes

### **2. 🎨 MATERIAL DESIGN 3 & FLUTTER BEST PRACTICES**
- Follow Material Design 3 principles and guidelines
- Use Flutter best practices for widget composition and state management
- Implement proper accessibility with semantic labels and tooltips
- Optimize performance with const constructors and efficient rebuilds

### **3. 📱 SPACE EFFICIENCY & CLEAN UX**
- Prioritize vertical space optimization in all UI components
- Choose icons with tooltips over text labels for modern, clean look
- Use lowercase naming conventions (e.g., 'Char' not 'CHAR')

## 🏗️ **ARCHITECTURE & CODE QUALITY**
## 🏗️ **ACTUAL PROJECT STRUCTURE** (Updated 2025)

```
lib/
├── config/                           # 🎨 Design & Configuration
│   ├── design_system.dart            # Core design system with manufacturer adaptations
│   ├── design_system_extensions.dart # Specialized design patterns
│   ├── adaptive_icons.dart           # Platform-adaptive icons
│   ├── color_system.dart             # WCAG AAA compliant colors
│   ├── navigation_system.dart        # Navigation patterns
│   ├── platform_adaptations.dart     # Cross-platform adaptations
│   ├── responsive_system.dart        # Responsive design utilities
│   └── shared_preference_provider.dart # Global app preferences
├── dao/                              # 🗄️ Data Access Objects
│   ├── database.dart                 # Core database operations
│   ├── book.dart                     # Book data operations
│   ├── book_note.dart                # Notes data operations
│   ├── reading_time.dart             # Reading time tracking
│   └── theme.dart                    # Theme persistence
├── enums/                            # 📝 Type Definitions
│   ├── ai_prompts.dart               # AI interaction types
│   ├── ai_role.dart                  # AI role definitions
│   ├── convert_chinese_mode.dart     # Chinese text conversion modes
│   ├── reading_info.dart             # Reading state enums
│   ├── sync_direction.dart           # Data sync directions
│   └── text_selection_mode.dart      # Text selection behaviors
├── l10n/                             # 🌍 Internationalization
│   ├── app_en.arb                    # English translations
│   ├── app_zh-CN.arb                 # Simplified Chinese
│   ├── app_zh-TW.arb                 # Traditional Chinese
│   ├── app_tr.arb                    # Turkish translations
│   └── generated/                    # Auto-generated i18n files
├── models/                           # 📊 Data Models
│   ├── book.dart                     # Book entity model
│   ├── book_style.dart               # Reading style preferences
│   ├── reading_rules.dart            # Reading behavior rules
│   ├── hsk_character.dart            # HSK character data (with Freezed)
│   ├── ai_message.dart               # AI chat messages (with Freezed)
│   ├── bookmark.dart                 # Bookmark data (with Freezed)
│   ├── window_info.dart              # Window state (with Freezed)
│   └── dictionary/                   # Dictionary-related models
├── page/                             # 📱 Main Application Pages
│   ├── home_page/                    # Main dashboard and navigation
│   ├── book_player/                  # EPUB reading interface
│   ├── settings_page/                # Settings and preferences
│   ├── book_detail.dart              # Book information page
│   ├── dictionary_page.dart          # Dictionary lookup interface
│   └── reading_page.dart             # Reading interface controller
├── providers/                        # 🔄 Riverpod State Management
│   ├── book_list.dart                # Book collection state
│   ├── ai_chat.dart                  # AI conversation state
│   ├── bookmark.dart                 # Bookmark management
│   ├── fonts.dart                    # Font management (with Freezed)
│   ├── hsk_providers.dart            # HSK learning state
│   ├── statistic_data.dart           # Reading statistics
│   └── storage_info.dart             # Storage management
├── service/                          # 🛠️ Business Logic Services
│   ├── ai/                           # AI integration services
│   │   ├── claude.dart               # Claude AI integration
│   │   ├── deepseek.dart             # DeepSeek AI integration
│   │   ├── gemini.dart               # Gemini AI integration
│   │   └── openai.dart               # OpenAI integration
│   ├── book_player/                  # EPUB rendering services
│   ├── convert_to_epub/              # Format conversion services
│   ├── dictionary/                   # Dictionary lookup services
│   ├── tts/                          # Text-to-speech services
│   ├── cache/                        # Caching services
│   ├── device/                       # Device information services
│   ├── notes/                        # Note management services
│   └── translate/                    # Translation services
├── utils/                            # 🔧 Utility Functions
│   ├── log/                          # Logging system (AnxLog)
│   ├── toast/                        # Toast notification utilities
│   ├── performance/                  # Performance monitoring
│   ├── accessibility/                # Accessibility helpers
│   ├── state_management/             # Advanced state management utilities
│   ├── error/                        # Error handling utilities
│   ├── ui/                           # UI helper functions
│   └── webdav/                       # WebDAV sync utilities
└── widgets/                          # 🎨 Reusable UI Components
    ├── context_menu/                 # Context menu components
    │   └── unified_context_menu.dart # Optimized unified context menu
    ├── reading_page/                 # Reading interface components
    ├── bookshelf/                    # Book list components
    ├── dictionary/                   # Dictionary UI components
    ├── settings/                     # Settings UI components
    ├── common/                       # Shared UI components
    ├── navigation/                   # Navigation components
    ├── book_notes/                   # Note-taking components
    ├── statistic/                    # Statistics visualization
    └── decorations/                  # UI decoration components
```


### **Code Standards**
```dart
// ✅ GOOD - Use DesignSystem constants
EdgeInsets.all(DesignSystem.spaceM)
// ❌ BAD - Hardcoded values
EdgeInsets.all(16.0)
```

- Follow lint rules in `analysis_options.yaml`
- Use `dart format` for consistent formatting
- Document public APIs with Dartdoc comments (`///`)
- Keep functions short and focused on single responsibility
- Minimize code duplication using functions, classes, mixins

### **Widget Best Practices**
- Use `const` constructors when possible to prevent unnecessary rebuilds
- Break down large widgets into smaller, manageable sub-widgets
- Call `setState()` only when necessary and as locally as possible
- Use proper keys for widget lists and state preservation
- Prefer StatelessWidget when no internal state management needed

### **State Management (Riverpod)**
- Use `Provider` for simple dependencies
- Use `StateProvider` for simple state
- Use `StateNotifierProvider` for complex state
- Use `FutureProvider` for async data
- Implement proper disposal to prevent memory leaks

## 🎨 **DESIGN SYSTEM COMPLIANCE**

### **Mandatory DesignSystem Usage**
```dart
// ✅ ALWAYS use DesignSystem constants
padding: EdgeInsets.all(DesignSystem.spaceM)
borderRadius: BorderRadius.circular(DesignSystem.radiusM)
// ✅ Use adaptive methods for responsive design
padding: DesignSystem.getAdaptivePadding(context)
```

### **Pixel-Perfect Manufacturer Adjustments**
```dart
// ✅ Use manufacturer-specific adjustments
fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600)
iconSize: DesignSystem.getAdjustedIconSize(24.0)
elevation: DesignSystem.getAdjustedElevation(4.0)
```

### **Accessibility Requirements**
- 44dp minimum touch targets for all interactive elements
- Proper semantic labels for screen readers
- Tooltips for icons to provide context
- WCAG AAA compliance for color contrast (7:1 ratio)

## 📱 **PLATFORM ADAPTATIONS**

### **Use Adaptive Components**
```dart
// ✅ Platform-appropriate navigation
AdaptiveNavigation.push(context, MyPage());
// ✅ Platform-specific icons
Icon(AdaptiveIcons.back)
Icon(AdaptiveIcons.settings)
```

### **Platform-Specific Behaviors**
- **iOS**: CupertinoPageRoute, flat design, BouncingScrollPhysics
- **Android**: MaterialPageRoute, elevation, ClampingScrollPhysics
- **Cross-platform consistency** while respecting platform conventions

## ⚡ **PERFORMANCE & OPTIMIZATION**

### **Performance Targets**
- Widget rebuild time: < 16ms (60 FPS)
- App startup time: < 3 seconds cold start
- Memory usage: < 150MB on average devices
- APK size: < 50MB after optimization

### **Error Handling**
```dart
// ✅ Proper error handling
try {
  await riskyOperation();
} catch (e) {
  AnxLog.severe('Operation failed: $e');
  AnxToast.show(L10n.of(context).error_message);
}
```

## 🔄 **STATE MANAGEMENT WITH RIVERPOD**

### **Provider Patterns**
```dart
// ✅ Simple state
final counterProvider = StateProvider<int>((ref) => 0);

// ✅ Code generation with riverpod_annotation
@riverpod
class BookList extends _$BookList {
  @override
  Future<List<Book>> build() async {
    return await BookService.getBooks();
  }
}
```

## 🌍 **CHINESE LANGUAGE FEATURES**

### **Core Chinese Support**
- Support both simplified and traditional Chinese text conversion
- Implement proper Chinese font rendering with `chinese_font_library`
- Handle polyphonic characters correctly with proper pronunciation
- Provide proper pinyin display and pronunciation support

### **HSK Learning Integration**
- HSK character data models with Freezed for immutability
- Character stroke order animation with `stroke_order_animator`
- Pinyin conversion using `pinyin` package
- Character segmentation for proper text analysis

## 📚 **E-READER FUNCTIONALITY**

### **EPUB Rendering**
- Follow EPUB rendering standards with `flutter_inappwebview`
- Implement proper reading progress tracking
- Support multiple reading modes (day/night/sepia/auto)
- Handle proper text selection and highlighting

### **Reading Experience**
- Use the optimized unified context menu for text interactions
- Implement efficient chapter navigation
- Support dictionary lookups for selected text
- Provide reading statistics and progress tracking

## 📋 **QUALITY CHECKLIST**

Before submitting any code, ensure:
- [ ] No breaking changes - all existing functionality works
- [ ] DesignSystem compliance - no hardcoded values
- [ ] Accessibility - proper semantic labels and tooltips
- [ ] Performance - efficient rebuilds and memory usage
- [ ] Platform adaptation - works correctly on iOS and Android
- [ ] Documentation - clear comments and explanations
- [ ] Testing - appropriate test coverage
- [ ] Code quality - follows lint rules and best practices

## 🎯 **SPECIFIC PROJECT PREFERENCES**

### **Context Menu Guidelines**
- Unified context menu is a critical component - handle with extreme care
- Save vertical space by combining elements when possible
- Use icons with tooltips instead of text labels
- Maintain all existing functionality while improving UX

### **Chinese Learning App Specifics**
- Support both simplified and traditional Chinese text
- Implement proper Chinese font rendering with chinese_font_library
- Handle polyphonic characters correctly
- Provide proper pinyin display and pronunciation support

### **UI/UX Priorities**
1. Functionality preservation (highest priority)
2. Space efficiency and clean design
3. Accessibility and usability
4. Performance and responsiveness
5. Cross-platform consistency

## 📖 **QUICK REFERENCE**

### **Essential Imports**
```dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
```

### **Key Constants**
```dart
// Spacing: DesignSystem.spaceXS/S/M/L/XL (4/8/16/24/32)
// Radius: DesignSystem.radiusS/M/L (4/8/16)
// Elevation: DesignSystem.elevationS/M/L (2/4/8)
```

### **Performance Logging**
```dart
PerformanceLoggingMode.quiet    // Clean development (default)
PerformanceLoggingMode.verbose  // Performance debugging
```

## 🏆 **SUCCESS METRICS**
- ✅ Zero breaking changes to existing functionality
- ✅ 95%+ UI consistency across Android manufacturers
- ✅ WCAG AAA accessibility compliance
- ✅ Material Design 3 standards implementation
- ✅ Optimal performance across all devices

## 🏆 **FINAL REMINDERS**

### **Before Every Commit**
1. Run `dart format` and `flutter analyze`
2. Test on multiple screen sizes and manufacturers
3. Verify accessibility and performance
4. Confirm no breaking changes

### **Remember**
- User experience comes first - always prioritize functionality preservation
- Space efficiency matters - especially for mobile interfaces
- Accessibility is mandatory - not optional
- Performance is critical - monitor and optimize continuously
- Consistency is key - follow established patterns

---

*These guidelines ensure consistent, professional, and high-quality development for the DassoShu Reader project.*

**Version**: 2.0 Condensed | **Last Updated**: Jun 2025

