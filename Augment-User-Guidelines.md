# 🚀 DassoShu Reader - Comprehensive Flutter Development Guidelines

## 📋 Project Overview

**DassoShu Reader** is a professional Chinese language learning e-book reader built with Flutter 3.x, featuring Material 3 design, advanced state management with Riverpod, and comprehensive cross-device compatibility.

### 🎯 Core Technologies
- **Flutter SDK**: `>=3.5.2 <4.0.0`
- **Material Design**: Material 3 with `useMaterial3: true`
- **State Management**: Flutter Riverpod 2.5.1+ with code generation
- **Localization**: Flutter's built-in i18n with ARB files
- **Chinese Support**: `chinese_font_library` for proper font rendering
- **Theme System**: `flex_color_scheme` for advanced theming

---

## 🏗️ **ACTUAL PROJECT STRUCTURE** (Updated 2024)

```
lib/
├── config/                           # 🎨 Design & Configuration
│   ├── design_system.dart            # Core design system with manufacturer adaptations
│   ├── design_system_extensions.dart # Specialized design patterns
│   ├── adaptive_icons.dart           # Platform-adaptive icons
│   ├── color_system.dart             # WCAG AAA compliant colors
│   ├── navigation_system.dart        # Navigation patterns
│   ├── platform_adaptations.dart     # Cross-platform adaptations
│   ├── responsive_system.dart        # Responsive design utilities
│   └── shared_preference_provider.dart # Global app preferences
├── dao/                              # 🗄️ Data Access Objects
│   ├── database.dart                 # Core database operations
│   ├── book.dart                     # Book data operations
│   ├── book_note.dart                # Notes data operations
│   ├── reading_time.dart             # Reading time tracking
│   └── theme.dart                    # Theme persistence
├── enums/                            # 📝 Type Definitions
│   ├── ai_prompts.dart               # AI interaction types
│   ├── ai_role.dart                  # AI role definitions
│   ├── convert_chinese_mode.dart     # Chinese text conversion modes
│   ├── reading_info.dart             # Reading state enums
│   ├── sync_direction.dart           # Data sync directions
│   └── text_selection_mode.dart      # Text selection behaviors
├── l10n/                             # 🌍 Internationalization
│   ├── app_en.arb                    # English translations
│   ├── app_zh-CN.arb                 # Simplified Chinese
│   ├── app_zh-TW.arb                 # Traditional Chinese
│   ├── app_tr.arb                    # Turkish translations
│   └── generated/                    # Auto-generated i18n files
├── models/                           # 📊 Data Models
│   ├── book.dart                     # Book entity model
│   ├── book_style.dart               # Reading style preferences
│   ├── reading_rules.dart            # Reading behavior rules
│   ├── hsk_character.dart            # HSK character data (with Freezed)
│   ├── ai_message.dart               # AI chat messages (with Freezed)
│   ├── bookmark.dart                 # Bookmark data (with Freezed)
│   ├── window_info.dart              # Window state (with Freezed)
│   └── dictionary/                   # Dictionary-related models
├── page/                             # 📱 Main Application Pages
│   ├── home_page/                    # Main dashboard and navigation
│   ├── book_player/                  # EPUB reading interface
│   ├── settings_page/                # Settings and preferences
│   ├── book_detail.dart              # Book information page
│   ├── dictionary_page.dart          # Dictionary lookup interface
│   └── reading_page.dart             # Reading interface controller
├── providers/                        # 🔄 Riverpod State Management
│   ├── book_list.dart                # Book collection state
│   ├── ai_chat.dart                  # AI conversation state
│   ├── bookmark.dart                 # Bookmark management
│   ├── fonts.dart                    # Font management (with Freezed)
│   ├── hsk_providers.dart            # HSK learning state
│   ├── statistic_data.dart           # Reading statistics
│   └── storage_info.dart             # Storage management
├── service/                          # 🛠️ Business Logic Services
│   ├── ai/                           # AI integration services
│   │   ├── claude.dart               # Claude AI integration
│   │   ├── deepseek.dart             # DeepSeek AI integration
│   │   ├── gemini.dart               # Gemini AI integration
│   │   └── openai.dart               # OpenAI integration
│   ├── book_player/                  # EPUB rendering services
│   ├── convert_to_epub/              # Format conversion services
│   ├── dictionary/                   # Dictionary lookup services
│   ├── tts/                          # Text-to-speech services
│   ├── cache/                        # Caching services
│   ├── device/                       # Device information services
│   ├── notes/                        # Note management services
│   └── translate/                    # Translation services
├── utils/                            # 🔧 Utility Functions
│   ├── log/                          # Logging system (AnxLog)
│   ├── toast/                        # Toast notification utilities
│   ├── performance/                  # Performance monitoring
│   ├── accessibility/                # Accessibility helpers
│   ├── state_management/             # Advanced state management utilities
│   ├── error/                        # Error handling utilities
│   ├── ui/                           # UI helper functions
│   └── webdav/                       # WebDAV sync utilities
└── widgets/                          # 🎨 Reusable UI Components
    ├── context_menu/                 # Context menu components
    │   └── unified_context_menu.dart # Optimized unified context menu
    ├── reading_page/                 # Reading interface components
    ├── bookshelf/                    # Book list components
    ├── dictionary/                   # Dictionary UI components
    ├── settings/                     # Settings UI components
    ├── common/                       # Shared UI components
    ├── navigation/                   # Navigation components
    ├── book_notes/                   # Note-taking components
    ├── statistic/                    # Statistics visualization
    └── decorations/                  # UI decoration components
```

---

## 🎯 **CORE PRINCIPLES** (ALWAYS FOLLOW)

### **1. 🚫 NEVER BREAK EXISTING FUNCTIONALITY**
- **Preserve everything** - maintain all existing features and behaviors
- **Respect existing architecture** - work within established patterns
- **Maintain backward compatibility** - ensure smooth upgrades
- **Test thoroughly** before implementing changes

### **2. 🎨 MATERIAL DESIGN 3 & FLUTTER BEST PRACTICES**
- **Follow Material Design 3** principles and guidelines
- **Use Flutter best practices** for widget composition and state management
- **Implement proper accessibility** with semantic labels and tooltips
- **Optimize performance** with const constructors and efficient rebuilds

### **3. 📱 SPACE EFFICIENCY & CLEAN UX**
- **Prioritize vertical space** optimization in all UI components
- **Choose icons with tooltips** over text labels for modern, clean look
- **Use lowercase naming** conventions (e.g., 'Char' not 'CHAR')

### **4. 🔧 PROFESSIONAL DEVELOPMENT APPROACH**
- **Ask before major changes** - present options with professional analysis
- **Provide detailed explanations** and implementation summaries
- **Present multiple solutions** with UI/UX recommendations
- **Implement exactly what user chooses** after presenting options

---

## 🏗️ **ARCHITECTURE & CODE QUALITY**

### **Code Standards**
```dart
// ✅ GOOD - Use DesignSystem constants
EdgeInsets.all(DesignSystem.spaceM)

// ❌ BAD - Hardcoded values
EdgeInsets.all(16.0)
```

- **Follow lint rules** in `analysis_options.yaml`
- **Use `dart format`** for consistent formatting
- **Document public APIs** with Dartdoc comments (`///`)
- **Keep functions short** and focused on single responsibility
- **Minimize code duplication** using functions, classes, mixins

### **Widget Best Practices**
- **Use `const` constructors** when possible to prevent unnecessary rebuilds
- **Break down large widgets** into smaller, manageable sub-widgets
- **Call `setState()`** only when necessary and as locally as possible
- **Use proper keys** for widget lists and state preservation
- **Prefer StatelessWidget** when no internal state management needed

### **State Management (Riverpod)**
- **Use `Provider`** for simple dependencies
- **Use `StateProvider`** for simple state
- **Use `StateNotifierProvider`** for complex state
- **Use `FutureProvider`** for async data
- **Implement proper disposal** to prevent memory leaks

---

## 🎨 **DESIGN SYSTEM COMPLIANCE**

### **Mandatory DesignSystem Usage**
```dart
// ✅ ALWAYS use DesignSystem constants
padding: EdgeInsets.all(DesignSystem.spaceM)
borderRadius: BorderRadius.circular(DesignSystem.radiusM)

// ✅ Use adaptive methods for responsive design
padding: DesignSystem.getAdaptivePadding(context)
```

### **Specialized Design Extensions**
- **SettingsDesign** - for settings pages
- **ReadingDesign** - for reading interface
- **WidgetDesign** - for reusable components

### **Pixel-Perfect Manufacturer Adjustments**
```dart
// ✅ Use manufacturer-specific adjustments
fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600)
iconSize: DesignSystem.getAdjustedIconSize(24.0)
elevation: DesignSystem.getAdjustedElevation(4.0)
```

### **Accessibility Requirements**
- **44dp minimum touch targets** for all interactive elements
- **Proper semantic labels** for screen readers
- **Tooltips for icons** to provide context
- **WCAG AAA compliance** for color contrast

---

## 📱 **PLATFORM ADAPTATIONS**

### **Use Adaptive Components**
```dart
// ✅ Platform-appropriate navigation
AdaptiveNavigation.push(context, MyPage());

// ✅ Platform-specific icons
Icon(AdaptiveIcons.back)
Icon(AdaptiveIcons.settings)

// ✅ Platform-appropriate dialogs
AdaptiveDialogs.showAlert(context: context, title: '...', content: '...');
```

### **Platform-Specific Behaviors**
- **iOS**: CupertinoPageRoute, flat design, BouncingScrollPhysics
- **Android**: MaterialPageRoute, elevation, ClampingScrollPhysics
- **Cross-platform consistency** while respecting platform conventions

---

## ⚡ **PERFORMANCE & OPTIMIZATION**

### **Performance Benchmarks & Targets**
```dart
// 🎯 PERFORMANCE TARGETS
- Widget rebuild time: < 16ms (60 FPS)
- App startup time: < 3 seconds cold start
- Memory usage: < 150MB on average devices
- APK size: < 50MB after optimization
- Network requests: < 5 seconds timeout
- Database queries: < 100ms for simple operations
- Image loading: < 2 seconds for cached images
```

### **Performance Monitoring**
```dart
// ✅ Use appropriate logging modes
PerformanceLoggingConfig.setMode(PerformanceLoggingMode.quiet); // Development
PerformanceLoggingConfig.setMode(PerformanceLoggingMode.verbose); // Debugging

// ✅ Debug mode configurations
if (kDebugMode) {
  // Enable performance overlay for frame rate monitoring
  MaterialApp(
    showPerformanceOverlay: true,
    debugShowCheckedModeBanner: false,
  );

  // Enable widget inspector
  WidgetsApp.debugAllowBannerOverride = false;

  // Enable detailed logging
  AnxLog.setLevel(LogLevel.verbose);
}
```

### **Performance Profiling**
```dart
// ✅ Profile widget rebuilds
class PerformanceProfiler {
  static void profileWidgetBuild(String widgetName, VoidCallback buildFunction) {
    final stopwatch = Stopwatch()..start();
    buildFunction();
    stopwatch.stop();

    if (stopwatch.elapsedMilliseconds > 16) {
      AnxLog.warning('Slow widget build: $widgetName took ${stopwatch.elapsedMilliseconds}ms');
    }
  }
}

// Usage in widgets
@override
Widget build(BuildContext context) {
  return PerformanceProfiler.profileWidgetBuild('MyWidget', () {
    return Container(/* widget content */);
  });
}
```

### **Error Handling**
```dart
// ✅ Proper error handling with error boundaries
try {
  await riskyOperation();
} catch (e) {
  AnxLog.severe('Operation failed: $e');
  // Provide user-friendly feedback
  AnxToast.show(L10n.of(context).error_message);

  // Report to crash analytics in release mode
  if (kReleaseMode) {
    FirebaseCrashlytics.instance.recordError(e, stackTrace);
  }
}
```

### **Memory Management**
- **Dispose controllers** and listeners properly
- **Use efficient scroll physics** and proper widget lifecycle
- **Monitor performance** using the performance dashboard
- **Optimize images** and assets for different screen densities
- **Profile memory usage** regularly with Flutter DevTools
- **Implement proper caching strategies** for images and data

---

## 🔒 **SECURITY & DATA HANDLING**

### **Secure Storage**
```dart
// ✅ Use secure storage for sensitive data
await FlutterSecureStorage().write(key: 'api_key', value: apiKey);
```

### **Network Security**
- **Use HTTPS** for all network communication
- **Validate all user input** on both client and server side
- **Handle API keys securely** without hardcoding in client-side code
- **Implement proper authentication** and authorization

---

## 🔄 **STATE MANAGEMENT WITH RIVERPOD**

### **Provider Patterns**
```dart
// ✅ Simple state
final counterProvider = StateProvider<int>((ref) => 0);

// ✅ Complex state with StateNotifier
final bookListProvider = StateNotifierProvider<BookListNotifier, List<Book>>((ref) {
  return BookListNotifier();
});

// ✅ Async data with FutureProvider
final booksProvider = FutureProvider<List<Book>>((ref) async {
  return await BookService.getBooks();
});

// ✅ Code generation with riverpod_annotation
@riverpod
class BookList extends _$BookList {
  @override
  Future<List<Book>> build() async {
    return await BookService.getBooks();
  }
}
```

### **State Management Rules**
- **Use SharedPreferences** for app settings and AI configurations
- **Implement proper stream management** for AI responses
- **Follow consistent state management pattern** with Riverpod across features
- **Use proper dependency injection** for services
- **Implement proper disposal** to prevent memory leaks

### **Advanced State Features**
- **State restoration** with automatic persistence
- **Race condition detection** and state consistency validation
- **Coordinated provider updates** with locking mechanisms
- **Automatic error recovery** and state rollback

---

## 🌍 **CHINESE LANGUAGE FEATURES**

### **Core Chinese Support**
- **Support both simplified and traditional** Chinese text conversion
- **Implement proper Chinese font rendering** with `chinese_font_library`
- **Handle polyphonic characters** correctly with proper pronunciation
- **Provide proper pinyin display** and pronunciation support

### **HSK Learning Integration**
- **HSK character data models** with Freezed for immutability
- **Character stroke order animation** with `stroke_order_animator`
- **Pinyin conversion** using `pinyin` package
- **Character segmentation** for proper text analysis

### **Dictionary Functionality**
- **Integrated dictionary lookup** for selected text
- **Character definitions and examples**
- **Pronunciation guides with audio**
- **HSK level indicators for characters**

---

## 📚 **E-READER FUNCTIONALITY**

### **EPUB Rendering**
- **Follow EPUB rendering standards** with `flutter_inappwebview`
- **Implement proper reading progress tracking**
- **Support multiple reading modes** (day/night/sepia/auto)
- **Handle proper text selection and highlighting**

### **Reading Experience**
- **Use the optimized unified context menu** for text interactions
- **Implement efficient chapter navigation**
- **Support dictionary lookups** for selected text
- **Provide reading statistics** and progress tracking

### **Text-to-Speech Integration**
- **Flutter TTS integration** for audio reading
- **Chinese pronunciation support**
- **Reading speed controls**
- **Audio service integration** for background playback

---

## 📝 **DOCUMENTATION & TESTING**

### **Documentation Requirements**
```dart
/// Builds a compact tab bar for the combined row layout.
///
/// This widget creates a space-efficient tab bar that combines
/// action buttons and tabs in a single row to save vertical space.
///
/// Example:
/// ```dart
/// _buildCompactTabBar(context, selectedText, bookId, readingTextColor)
/// ```
Widget _buildCompactTabBar(BuildContext context, String selectedText) {
  // Implementation
}
```

### **Testing Strategy**
- **Unit tests** for critical business logic
- **Widget tests** for important UI components
- **Integration tests** for complete user flows
- **Cross-device testing** on multiple manufacturers
- **Accessibility testing** with screen readers

---

## 🚀 **BUILD & DEPLOYMENT**

### **Version Control & Team Collaboration**
```bash
# ✅ Conventional Commits Format
git commit -m "feat(context-menu): add icons with tooltips for compact layout

- Replace text labels with Material Design icons
- Add tooltips for accessibility
- Implement space-efficient tab bar design
- Maintain all existing functionality

Closes #123"

# ✅ Branch Naming Convention
feature/context-menu-icons
bugfix/reading-progress-calculation
hotfix/critical-crash-fix
release/v1.2.0
```

### **Team Collaboration Guidelines**
- **Feature Branches**: Always create feature branches from `develop`
- **Code Reviews**: Require at least 2 approvals for main branch
- **Pull Request Template**: Use standardized PR descriptions
- **Architectural Decisions**: Document in ADR (Architecture Decision Records)
- **Daily Standups**: Share progress and blockers
- **Sprint Planning**: Estimate tasks using story points

### **Dependency Management & Migration**
```yaml
# ✅ Use package managers, never edit manually
dependencies:
  flutter:
    sdk: flutter
  riverpod: ^2.4.0  # Use caret for stable versions

# ✅ Dependency Update Process
# 1. Check CHANGELOG for breaking changes
# 2. Update in separate branch
# 3. Run full test suite
# 4. Test on multiple devices
# 5. Update documentation if needed
```

### **Migration Strategies**
```dart
// ✅ When updating major dependencies:
class MigrationChecklist {
  static const steps = [
    '1. Backup current working state',
    '2. Create migration branch',
    '3. Update dependencies incrementally',
    '4. Fix breaking changes',
    '5. Update code generation files (build_runner)',
    '6. Run full test suite',
    '7. Test on multiple devices and manufacturers',
    '8. Verify performance impact',
    '9. Update documentation',
    '10. Create migration guide for team',
  ];
}
```

### **Build Process & CI/CD**
```yaml
# ✅ CI/CD Pipeline Configuration
stages:
  - lint_and_format
  - unit_tests
  - widget_tests
  - integration_tests
  - build_android
  - build_ios
  - performance_tests
  - security_scan
  - deploy_staging
  - deploy_production

# Build optimization
flutter build apk --release --shrink --obfuscate --split-debug-info=debug-info/
```

### **Build Optimization**
- **Use Flutter flavors** for different environments (dev, staging, prod)
- **Implement proper signing** configuration for release builds
- **Automate versioning** and build process with CI/CD
- **Generate build reports** for size and performance metrics
- **Enable code obfuscation** for release builds
- **Implement app bundle** for Play Store optimization
- **Monitor app size** and performance impact of changes

---

## 🎯 **SPECIFIC PROJECT PREFERENCES**

### **Context Menu Guidelines**
- **Unified context menu** is a critical component - handle with extreme care
- **Save vertical space** by combining elements when possible
- **Use icons with tooltips** instead of text labels
- **Maintain all existing functionality** while improving UX

### **Chinese Learning App Specifics**
- **Support both simplified and traditional** Chinese text
- **Implement proper Chinese font rendering** with chinese_font_library
- **Handle polyphonic characters** correctly
- **Provide proper pinyin display** and pronunciation support

### **UI/UX Priorities**
1. **Functionality preservation** (highest priority)
2. **Space efficiency** and clean design
3. **Accessibility** and usability
4. **Performance** and responsiveness
5. **Cross-platform consistency**

---

## 📋 **COMPREHENSIVE CODE REVIEW CHECKLIST**

### **Pre-Submission Checklist**
Before submitting any code, ensure:
- [ ] **No breaking changes** - all existing functionality works
- [ ] **DesignSystem compliance** - no hardcoded values
- [ ] **Accessibility** - proper semantic labels and tooltips
- [ ] **Performance** - efficient rebuilds and memory usage
- [ ] **Platform adaptation** - works correctly on iOS and Android
- [ ] **Documentation** - clear comments and explanations
- [ ] **Testing** - appropriate test coverage
- [ ] **Code quality** - follows lint rules and best practices

### **Detailed Code Review Checklist**
#### **🔍 Code Quality**
- [ ] No hardcoded strings (use L10n for all user-facing text)
- [ ] No magic numbers (use DesignSystem constants)
- [ ] Proper null safety implementation
- [ ] No unused imports or variables
- [ ] Consistent naming conventions followed
- [ ] Functions are single-purpose and well-named
- [ ] Classes follow single responsibility principle

#### **🎨 UI/UX Standards**
- [ ] All interactive elements have 44dp minimum touch targets
- [ ] Proper semantic labels for screen readers
- [ ] Tooltips provided for icon-only buttons
- [ ] Color contrast meets WCAG AAA standards (7:1 ratio)
- [ ] Responsive design works on different screen sizes
- [ ] Dark mode support implemented correctly
- [ ] Loading states and error states handled

#### **⚡ Performance Standards**
- [ ] Widget rebuilds are optimized (const constructors used)
- [ ] No expensive operations in build() methods
- [ ] Proper disposal of controllers and listeners
- [ ] Images are optimized and cached appropriately
- [ ] Network requests have proper timeout handling
- [ ] Database queries are efficient
- [ ] Memory leaks prevented

#### **🔒 Security & Data**
- [ ] No sensitive data in logs
- [ ] API keys stored securely
- [ ] User input validated and sanitized
- [ ] HTTPS used for all network communication
- [ ] Proper authentication and authorization

#### **🧪 Testing Standards**
- [ ] Unit tests for business logic
- [ ] Widget tests for UI components
- [ ] Integration tests for user flows
- [ ] Accessibility testing completed
- [ ] Cross-device testing performed
- [ ] Performance impact assessed

#### **📱 Platform Compatibility**
- [ ] iOS and Android behavior tested
- [ ] Platform-specific adaptations implemented
- [ ] Different screen densities supported
- [ ] Various Android manufacturers tested
- [ ] Keyboard navigation works properly

---

## 🔧 **IMPLEMENTATION EXAMPLES**

### **Correct DesignSystem Usage**
```dart
// ✅ CORRECT - Using DesignSystem constants
Container(
  padding: EdgeInsets.all(DesignSystem.spaceM),
  margin: EdgeInsets.symmetric(
    horizontal: DesignSystem.spaceS,
    vertical: DesignSystem.spaceXS,
  ),
  decoration: BoxDecoration(
    borderRadius: BorderRadius.circular(DesignSystem.radiusM),
    elevation: DesignSystem.elevationS,
  ),
  child: Text(
    'Content',
    style: TextStyle(
      fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600),
    ),
  ),
)

// ❌ INCORRECT - Hardcoded values
Container(
  padding: EdgeInsets.all(16.0),
  margin: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
  decoration: BoxDecoration(
    borderRadius: BorderRadius.circular(12.0),
    elevation: 2.0,
  ),
  child: Text(
    'Content',
    style: TextStyle(fontWeight: FontWeight.w600),
  ),
)
```

### **Proper State Management**
```dart
// ✅ CORRECT - Using Riverpod providers
final selectedTabProvider = StateProvider<int>((ref) => 0);

class TabWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedTab = ref.watch(selectedTabProvider);
    return TabBar(
      onTap: (index) => ref.read(selectedTabProvider.notifier).state = index,
      tabs: tabs,
    );
  }
}

// ❌ INCORRECT - Direct state management without providers
class TabWidget extends StatefulWidget {
  @override
  _TabWidgetState createState() => _TabWidgetState();
}

class _TabWidgetState extends State<TabWidget> {
  int selectedTab = 0;

  @override
  Widget build(BuildContext context) {
    return TabBar(
      onTap: (index) => setState(() => selectedTab = index),
      tabs: tabs,
    );
  }
}
```

### **Adaptive Component Usage**
```dart
// ✅ CORRECT - Platform-adaptive components
AdaptiveListTile(
  leading: Icon(AdaptiveIcons.settings),
  title: Text('Settings'),
  trailing: Icon(AdaptiveIcons.chevronRight),
  onTap: () => AdaptiveNavigation.push(context, SettingsPage()),
)

// ❌ INCORRECT - Platform-specific hardcoding
ListTile(
  leading: Icon(Platform.isIOS ? CupertinoIcons.settings : Icons.settings),
  title: Text('Settings'),
  trailing: Icon(Platform.isIOS ? CupertinoIcons.chevron_right : Icons.chevron_right),
  onTap: () => Navigator.push(
    context,
    Platform.isIOS
      ? CupertinoPageRoute(builder: (context) => SettingsPage())
      : MaterialPageRoute(builder: (context) => SettingsPage()),
  ),
)
```

---

## �️ **DEBUGGING & TROUBLESHOOTING**

### **Common Development Issues**
```dart
// ✅ Debug Widget Rebuilds
class DebugHelper {
  static void logWidgetRebuild(String widgetName) {
    if (kDebugMode) {
      AnxLog.debug('🔄 Widget rebuilt: $widgetName at ${DateTime.now()}');
    }
  }

  static void logPerformanceIssue(String operation, Duration duration) {
    if (duration.inMilliseconds > 100) {
      AnxLog.warning('⚠️ Slow operation: $operation took ${duration.inMilliseconds}ms');
    }
  }
}
```

### **Troubleshooting Guide**
| Issue | Symptoms | Solution |
|-------|----------|----------|
| **Memory Leaks** | App crashes, high memory usage | Check controller disposal, use DevTools |
| **Slow Rebuilds** | Janky animations, poor performance | Profile with Flutter Inspector, optimize build methods |
| **State Issues** | Inconsistent UI, data loss | Verify provider dependencies, check state flow |
| **Layout Overflow** | RenderFlex overflow errors | Use responsive design, check DesignSystem usage |
| **Font Rendering** | Chinese characters not displaying | Verify chinese_font_library integration |

### **Performance Debugging**
```dart
// ✅ Profile specific operations
Future<T> profileOperation<T>(String name, Future<T> Function() operation) async {
  final stopwatch = Stopwatch()..start();
  try {
    final result = await operation();
    stopwatch.stop();

    if (kDebugMode) {
      AnxLog.info('⏱️ $name completed in ${stopwatch.elapsedMilliseconds}ms');
    }

    return result;
  } catch (e) {
    stopwatch.stop();
    AnxLog.severe('❌ $name failed after ${stopwatch.elapsedMilliseconds}ms: $e');
    rethrow;
  }
}
```

---

## �📖 **QUICK REFERENCE**

### **Essential Imports**
```dart
// Core Flutter
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

// State Management
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project Core
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/widgets/common/adaptive_navigation.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
```

### **Key Constants**
```dart
// Spacing
DesignSystem.spaceXS  // 4.0
DesignSystem.spaceS   // 8.0
DesignSystem.spaceM   // 16.0
DesignSystem.spaceL   // 24.0
DesignSystem.spaceXL  // 32.0

// Border Radius
DesignSystem.radiusS  // 4.0
DesignSystem.radiusM  // 8.0
DesignSystem.radiusL  // 12.0

// Elevation
DesignSystem.elevationS  // 2.0
DesignSystem.elevationM  // 4.0
DesignSystem.elevationL  // 8.0
```

### **Performance Logging Modes**
```dart
PerformanceLoggingMode.quiet    // Clean development (default)
PerformanceLoggingMode.minimal  // Basic info only
PerformanceLoggingMode.verbose  // Performance debugging
PerformanceLoggingMode.debug    // Full analysis
```

---

## 🏆 **SUCCESS METRICS**

- ✅ **Zero breaking changes** to existing functionality
- ✅ **95%+ UI consistency** across Android manufacturers
- ✅ **Professional-grade responsive design**
- ✅ **WCAG AAA accessibility compliance**
- ✅ **Material Design 3 standards** implementation
- ✅ **Optimal performance** across all devices

---

## 🏆 **FINAL REMINDERS**

### **Before Every Commit**
1. ✅ Run `dart format` to ensure consistent formatting
2. ✅ Run `flutter analyze` to check for lint issues
3. ✅ Test on multiple screen sizes and manufacturers
4. ✅ Verify accessibility with screen reader
5. ✅ Confirm no breaking changes to existing functionality

### **Before Every Pull Request**
1. ✅ Update documentation if needed
2. ✅ Add appropriate tests for new functionality
3. ✅ Verify cross-platform compatibility
4. ✅ Check performance impact
5. ✅ Ensure security best practices are followed

### **Remember**
- **User experience comes first** - always prioritize functionality preservation
- **Space efficiency matters** - especially for mobile interfaces
- **Accessibility is mandatory** - not optional
- **Performance is critical** - monitor and optimize continuously
- **Consistency is key** - follow established patterns
- **Security is paramount** - protect user data and privacy
- **Testing is essential** - ensure quality through comprehensive testing
- **Documentation is valuable** - help future developers understand the code

---

## 📚 **GUIDELINES VERSION HISTORY**

### **Version 2.0** (Current) - December 2024
- ✅ Added comprehensive performance benchmarks and targets
- ✅ Enhanced code review checklist with detailed criteria
- ✅ Added debugging and troubleshooting section
- ✅ Included team collaboration guidelines
- ✅ Added migration strategies for dependency updates
- ✅ Enhanced CI/CD pipeline documentation
- ✅ Added performance profiling tools and techniques
- ✅ Included security best practices
- ✅ Added version history tracking

### **Version 1.0** - Initial Release
- ✅ Basic project structure documentation
- ✅ Core principles and architecture guidelines
- ✅ Design system compliance rules
- ✅ Chinese language features documentation
- ✅ E-reader functionality guidelines
- ✅ Basic quality checklist

---

## 🎯 **CONTINUOUS IMPROVEMENT**

### **Quarterly Reviews**
- Review and update guidelines based on team feedback
- Incorporate new Flutter best practices and features
- Update performance benchmarks based on real-world data
- Refine troubleshooting guide with common issues encountered

### **Feedback Loop**
- Collect developer feedback on guideline effectiveness
- Track metrics: code review time, bug rates, performance improvements
- Update guidelines based on lessons learned from production issues
- Share knowledge across team through regular tech talks

---

*These comprehensive guidelines ensure consistent, professional, and high-quality development for the DassoShu Reader project. Follow them religiously to deliver the best possible results and maintain the project's excellence standards.*

**Last Updated**: December 2024 | **Version**: 2.0 | **Next Review**: March 2025
```