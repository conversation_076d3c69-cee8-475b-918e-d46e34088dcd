import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dasso_reader/utils/state_management/state_flow_debugger.dart';
import 'package:dasso_reader/utils/state_management/state_consistency.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// Comprehensive predictability integration system for Dasso Reader
///
/// This system integrates all predictability features:
/// - State flow debugging and visualization
/// - Race condition detection and prevention
/// - State consistency validation
/// - Enhanced logging and monitoring

/// Predictability system configuration
class PredictabilityConfig {
  final bool enableStateFlowDebugging;
  final bool enableRaceConditionDetection;
  final bool enableStateConsistency;
  final bool enableDebugOverlay;
  final bool enableVisualization;
  final Set<String> criticalProviders;

  const PredictabilityConfig({
    this.enableStateFlowDebugging = kDebugMode,
    this.enableRaceConditionDetection = true,
    this.enableStateConsistency = true,
    this.enableDebugOverlay = kDebugMode,
    this.enableVisualization = kDebugMode,
    this.criticalProviders = const {
      'bookList',
      'aiChat',
      'readingState',
      'userPreferences',
    },
  });
}

/// Main predictability system manager
class PredictabilitySystem {
  static PredictabilitySystem? _instance;
  static PredictabilitySystem get instance =>
      _instance ??= PredictabilitySystem._();

  PredictabilitySystem._();

  PredictabilityConfig _config = const PredictabilityConfig();
  bool _initialized = false;

  /// Initialize the predictability system
  Future<void> initialize({PredictabilityConfig? config}) async {
    if (_initialized) return;

    _config = config ?? const PredictabilityConfig();

    try {
      // Initialize state flow debugger
      if (_config.enableStateFlowDebugging) {
        StateFlowDebugger.instance.initialize(
          config: StateFlowDebugConfig(
            enabled: true,
            trackDependencies: true,
            enableVisualizer: _config.enableVisualization,
            enableOverlay: _config.enableDebugOverlay,
            trackedProviders: _config.criticalProviders,
          ),
        );
      }

      // Initialize race condition detector
      if (_config.enableRaceConditionDetection) {
        // Race condition detector is automatically initialized
        _setupRaceConditionMonitoring();
      }

      // Initialize state consistency validator
      if (_config.enableStateConsistency) {
        _setupStateConsistencyRules();
      }

      // Initialize state lock manager
      StateLockManager.instance.initialize();

      _initialized = true;

      if (kDebugMode) {
        AnxLog.info('🎯 Predictability System initialized successfully');
        _logSystemStatus();
      }
    } catch (e) {
      AnxLog.severe('Failed to initialize Predictability System: $e');
    }
  }

  /// Setup race condition monitoring
  void _setupRaceConditionMonitoring() {
    RaceConditionDetector.instance.detectionStream.listen((detection) {
      if (kDebugMode) {
        AnxLog.warning(
          '⚠️ Race condition detected: ${detection.providerId} - ${detection.description}',
        );
      }

      // Handle critical race conditions
      if (detection.severity == 'critical') {
        _handleCriticalRaceCondition(detection);
      }
    });
  }

  /// Setup state consistency validation rules
  void _setupStateConsistencyRules() {
    final validator = StateConsistencyValidator.instance;

    // Add book list consistency rule
    validator.addRule('bookList', BookListConsistencyRule());

    // Add AI chat consistency rule
    validator.addRule('aiChat', AiChatConsistencyRule());

    // Add reading state consistency rule
    validator.addRule('readingState', ReadingStateConsistencyRule());

    if (kDebugMode) {
      AnxLog.info('📋 State consistency rules configured');
    }
  }

  /// Handle critical race conditions
  void _handleCriticalRaceCondition(RaceConditionDetection detection) {
    // Log critical race condition
    AnxLog.severe(
      'Critical race condition in ${detection.providerId}: ${detection.description}',
    );

    // Implement recovery strategy based on provider type
    if (detection.providerId.contains('book')) {
      _recoverBookListState();
    } else if (detection.providerId.contains('ai')) {
      _recoverAiChatState();
    }
  }

  /// Recover book list state
  void _recoverBookListState() {
    if (kDebugMode) {
      AnxLog.info('🔄 Attempting to recover book list state');
    }
    // Implementation would depend on your specific book list provider
    // This is a placeholder for the recovery logic
  }

  /// Recover AI chat state
  void _recoverAiChatState() {
    if (kDebugMode) {
      AnxLog.info('🔄 Attempting to recover AI chat state');
    }
    // Implementation would depend on your specific AI chat provider
    // This is a placeholder for the recovery logic
  }

  /// Log system status
  void _logSystemStatus() {
    final status = StringBuffer();
    status.writeln('🎯 PREDICTABILITY SYSTEM STATUS');
    status.writeln('================================');
    status.writeln(
        'State Flow Debugging: ${_config.enableStateFlowDebugging ? "✅" : "❌"}');
    status.writeln(
        'Race Condition Detection: ${_config.enableRaceConditionDetection ? "✅" : "❌"}');
    status.writeln(
        'State Consistency: ${_config.enableStateConsistency ? "✅" : "❌"}');
    status.writeln('Debug Overlay: ${_config.enableDebugOverlay ? "✅" : "❌"}');
    status.writeln('Visualization: ${_config.enableVisualization ? "✅" : "❌"}');
    status
        .writeln('Critical Providers: ${_config.criticalProviders.join(", ")}');
    status.writeln('================================');

    debugPrint(status.toString());
  }

  /// Get system health report
  Map<String, dynamic> getHealthReport() {
    final debugger = StateFlowDebugger.instance;
    final raceDetector = RaceConditionDetector.instance;
    final validator = StateConsistencyValidator.instance;

    return {
      'timestamp': DateTime.now().toIso8601String(),
      'initialized': _initialized,
      'config': {
        'stateFlowDebugging': _config.enableStateFlowDebugging,
        'raceConditionDetection': _config.enableRaceConditionDetection,
        'stateConsistency': _config.enableStateConsistency,
        'debugOverlay': _config.enableDebugOverlay,
        'visualization': _config.enableVisualization,
      },
      'statistics': {
        'trackedProviders': debugger.getTrackedProviders().length,
        'stateChanges': debugger.getStateHistory().length,
        'raceConditions': raceDetector.getDetectionHistory().length,
        'validationResults': validator.getValidationHistory().length,
      },
      'recentActivity': {
        'lastStateChange': debugger.getStateHistory().isNotEmpty
            ? debugger.getStateHistory().last.timestamp.toIso8601String()
            : null,
        'lastRaceCondition': raceDetector.getDetectionHistory().isNotEmpty
            ? raceDetector
                .getDetectionHistory()
                .last
                .detectedAt
                .toIso8601String()
            : null,
      },
    };
  }

  /// Dispose resources
  void dispose() {
    StateFlowDebugger.instance.dispose();
    StateLockManager.instance.dispose();
    _initialized = false;

    if (kDebugMode) {
      AnxLog.info('🧹 Predictability System disposed');
    }
  }
}

/// Reading state consistency rule
class ReadingStateConsistencyRule extends StateConsistencyRule {
  @override
  String get name => 'ReadingStateConsistency';

  @override
  String get description => 'Validates reading state consistency';

  @override
  StateConsistencyResult validate(dynamic state) {
    final violations = <String>[];

    // Add reading state validation logic based on your reading state structure
    if (state != null) {
      // Example validations - adjust based on your actual reading state
      if (state is Map<String, dynamic>) {
        // Check for required fields
        if (!state.containsKey('currentPosition')) {
          violations.add('Missing current position');
        }

        if (!state.containsKey('bookId')) {
          violations.add('Missing book ID');
        }

        // Validate position values
        if (state['currentPosition'] != null) {
          final position = state['currentPosition'];
          if (position is num && position < 0) {
            violations.add('Invalid negative position');
          }
        }
      }
    }

    return StateConsistencyResult(
      providerId: 'readingState',
      isValid: violations.isEmpty,
      violations: violations,
      validatedAt: DateTime.now(),
    );
  }
}

/// Enhanced provider wrapper with full predictability features
class PredictableProvider<T> extends StateNotifier<AsyncValue<T>> {
  final String providerId;
  final Future<T> Function() dataLoader;
  final List<StateConsistencyRule> validationRules;

  PredictableProvider({
    required this.providerId,
    required this.dataLoader,
    this.validationRules = const [],
  }) : super(const AsyncValue.loading()) {
    // Register with predictability system
    _registerWithSystem();

    // Load initial data
    _loadData();
  }

  /// Register with predictability system
  void _registerWithSystem() {
    // Track provider creation
    StateFlowDebugger.instance.trackStateChange(
      providerId: providerId,
      providerType: 'PredictableProvider',
      previousValue: null,
      newValue: 'created',
    );

    // Register validation rules
    for (final rule in validationRules) {
      StateConsistencyValidator.instance.addRule(providerId, rule);
    }
  }

  /// Load data with full predictability features
  Future<void> _loadData() async {
    final operationId = 'load_${DateTime.now().millisecondsSinceEpoch}';

    // Track operation for race condition detection
    RaceConditionDetector.instance.trackOperation(providerId, operationId);

    try {
      // Acquire lock to prevent race conditions
      final lock =
          await StateLockManager.instance.acquireLock(providerId, operationId);
      if (lock == null) {
        if (kDebugMode) {
          AnxLog.warning(
              'Could not acquire lock for $providerId, operation already in progress');
        }
        return;
      }

      try {
        final data = await dataLoader();

        // Validate state consistency
        final validationResult =
            StateConsistencyValidator.instance.validateState(providerId, data);

        if (validationResult.isValid) {
          final previousState = state;
          state = AsyncValue.data(data);

          // Track successful state change
          StateFlowDebugger.instance.trackStateChange(
            providerId: providerId,
            providerType: 'PredictableProvider',
            previousValue: previousState,
            newValue: data,
          );
        } else {
          final error = StateError(
              'State validation failed: ${validationResult.violations.join(', ')}');
          state = AsyncValue.error(error, StackTrace.current);

          AnxLog.severe('State validation failed for $providerId', error);
        }
      } finally {
        StateLockManager.instance.releaseLock(providerId);
      }
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      AnxLog.severe('Error loading data for $providerId', error, stackTrace);
    } finally {
      RaceConditionDetector.instance.completeOperation(providerId, operationId);
    }
  }

  /// Refresh data
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    await _loadData();
  }
}
