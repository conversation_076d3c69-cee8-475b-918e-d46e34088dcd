import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// Const constructor audit system for Dasso Reader
///
/// This system helps identify widgets that could benefit from const constructors
/// to improve performance by reducing unnecessary rebuilds.
class ConstAudit {
  static final Map<String, ConstAuditResult> _auditResults = {};

  /// Audit a widget for const constructor optimization potential
  static ConstAuditResult auditWidget(Widget widget, String debugName) {
    if (!kDebugMode) return ConstAuditResult.optimized(debugName);

    final result = _analyzeWidget(widget, debugName);
    _auditResults[debugName] = result;

    if (result.canBeOptimized) {
      debugPrint('🔍 Const Audit: $debugName can be optimized');
    }

    return result;
  }

  /// Analyze widget for const optimization potential
  static ConstAuditResult _analyzeWidget(Widget widget, String debugName) {
    // Check if widget is already const
    if (widget is StatelessWidget) {
      try {
        // Try to create a const version to test
        final runtimeType = widget.runtimeType;
        final isConst = widget.toString().contains('const');

        if (isConst) {
          return ConstAuditResult.optimized(debugName);
        } else {
          return ConstAuditResult.canOptimize(
              debugName, 'StatelessWidget can use const constructor');
        }
      } catch (e) {
        return ConstAuditResult.cannotOptimize(
            debugName, 'Widget has dynamic properties: $e');
      }
    }

    // StatefulWidget cannot be const
    if (widget is StatefulWidget) {
      return ConstAuditResult.cannotOptimize(
          debugName, 'StatefulWidget cannot be const');
    }

    // Check common widget types
    if (widget is Text) {
      return _auditTextWidget(widget, debugName);
    }

    if (widget is Icon) {
      return _auditIconWidget(widget, debugName);
    }

    if (widget is Container) {
      return _auditContainerWidget(widget, debugName);
    }

    if (widget is SizedBox) {
      return _auditSizedBoxWidget(widget, debugName);
    }

    return ConstAuditResult.unknown(debugName);
  }

  /// Audit Text widget for const optimization
  static ConstAuditResult _auditTextWidget(Text widget, String debugName) {
    // Text can be const if data and style are const
    final hasConstData = widget.data != null;
    final hasConstStyle =
        widget.style == null || _isConstTextStyle(widget.style!);

    if (hasConstData && hasConstStyle) {
      return ConstAuditResult.canOptimize(
          debugName, 'Text widget can be const');
    }

    return ConstAuditResult.cannotOptimize(
        debugName, 'Text has dynamic data or style');
  }

  /// Audit Icon widget for const optimization
  static ConstAuditResult _auditIconWidget(Icon widget, String debugName) {
    // Icon can be const if icon data is const
    if (widget.icon != null) {
      return ConstAuditResult.canOptimize(
          debugName, 'Icon widget can be const');
    }

    return ConstAuditResult.cannotOptimize(
        debugName, 'Icon has dynamic properties');
  }

  /// Audit Container widget for const optimization
  static ConstAuditResult _auditContainerWidget(
      Container widget, String debugName) {
    // Container is rarely const due to dynamic properties
    final hasChild = widget.child != null;
    final hasDecoration = widget.decoration != null;
    final hasConstraints = widget.constraints != null;

    if (!hasChild && !hasDecoration && !hasConstraints) {
      return ConstAuditResult.canOptimize(
          debugName, 'Simple Container can be const');
    }

    return ConstAuditResult.cannotOptimize(
        debugName, 'Container has dynamic properties');
  }

  /// Audit SizedBox widget for const optimization
  static ConstAuditResult _auditSizedBoxWidget(
      SizedBox widget, String debugName) {
    // SizedBox can often be const
    final hasConstDimensions = widget.width != null || widget.height != null;

    if (hasConstDimensions && widget.child == null) {
      return ConstAuditResult.canOptimize(
          debugName, 'SizedBox with const dimensions can be const');
    }

    return ConstAuditResult.cannotOptimize(
        debugName, 'SizedBox has dynamic properties');
  }

  /// Check if TextStyle can be const
  static bool _isConstTextStyle(TextStyle style) {
    // This is a simplified check - in practice, most TextStyles can be const
    // if they don't use dynamic values
    return true; // Simplified for this implementation
  }

  /// Get all audit results
  static Map<String, ConstAuditResult> getAuditResults() =>
      Map.from(_auditResults);

  /// Clear audit results
  static void clearAuditResults() => _auditResults.clear();

  /// Print audit summary
  static void printAuditSummary() {
    if (!kDebugMode || _auditResults.isEmpty) return;

    final canOptimize =
        _auditResults.values.where((result) => result.canBeOptimized).length;
    final total = _auditResults.length;

    debugPrint('\n📋 Const Constructor Audit Summary:');
    debugPrint('  Total widgets audited: $total');
    debugPrint('  Can be optimized: $canOptimize');
    debugPrint(
        '  Optimization potential: ${(canOptimize / total * 100).toStringAsFixed(1)}%');

    // Show top optimization candidates
    final candidates = _auditResults.entries
        .where((entry) => entry.value.canBeOptimized)
        .take(10)
        .toList();

    if (candidates.isNotEmpty) {
      debugPrint('\n  Top optimization candidates:');
      for (final candidate in candidates) {
        debugPrint('    ${candidate.key}: ${candidate.value.reason}');
      }
    }
    debugPrint('');
  }
}

/// Result of const constructor audit
class ConstAuditResult {
  const ConstAuditResult({
    required this.widgetName,
    required this.canBeOptimized,
    required this.reason,
    required this.status,
  });

  final String widgetName;
  final bool canBeOptimized;
  final String reason;
  final ConstAuditStatus status;

  factory ConstAuditResult.optimized(String widgetName) {
    return ConstAuditResult(
      widgetName: widgetName,
      canBeOptimized: false,
      reason: 'Already optimized',
      status: ConstAuditStatus.optimized,
    );
  }

  factory ConstAuditResult.canOptimize(String widgetName, String reason) {
    return ConstAuditResult(
      widgetName: widgetName,
      canBeOptimized: true,
      reason: reason,
      status: ConstAuditStatus.canOptimize,
    );
  }

  factory ConstAuditResult.cannotOptimize(String widgetName, String reason) {
    return ConstAuditResult(
      widgetName: widgetName,
      canBeOptimized: false,
      reason: reason,
      status: ConstAuditStatus.cannotOptimize,
    );
  }

  factory ConstAuditResult.unknown(String widgetName) {
    return ConstAuditResult(
      widgetName: widgetName,
      canBeOptimized: false,
      reason: 'Analysis inconclusive',
      status: ConstAuditStatus.unknown,
    );
  }
}

/// Status of const audit
enum ConstAuditStatus {
  optimized,
  canOptimize,
  cannotOptimize,
  unknown,
}

/// Mixin for widgets to enable const auditing
mixin ConstAuditMixin on Widget {
  String get auditName => runtimeType.toString();

  /// Audit this widget for const optimization
  ConstAuditResult auditConst() {
    return ConstAudit.auditWidget(this, auditName);
  }
}

/// Widget wrapper that performs const audit in debug mode
class ConstAuditWrapper extends StatelessWidget {
  const ConstAuditWrapper({
    super.key,
    required this.child,
    this.debugName,
  });

  final Widget child;
  final String? debugName;

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      final name = debugName ?? child.runtimeType.toString();
      ConstAudit.auditWidget(child, name);
    }
    return child;
  }
}

/// Extension to easily add const auditing to widgets
extension ConstAuditExtensions on Widget {
  /// Wrap widget with const audit in debug mode
  Widget withConstAudit([String? debugName]) {
    return kDebugMode
        ? ConstAuditWrapper(debugName: debugName, child: this)
        : this;
  }
}

/// Optimized const widgets for common use cases
class OptimizedWidgets {
  /// Const SizedBox for common spacing
  static const Widget verticalSpaceXS = SizedBox(height: 4);
  static const Widget verticalSpaceS = SizedBox(height: 8);
  static const Widget verticalSpaceM = SizedBox(height: 16);
  static const Widget verticalSpaceL = SizedBox(height: 24);
  static const Widget verticalSpaceXL = SizedBox(height: 32);

  static const Widget horizontalSpaceXS = SizedBox(width: 4);
  static const Widget horizontalSpaceS = SizedBox(width: 8);
  static const Widget horizontalSpaceM = SizedBox(width: 16);
  static const Widget horizontalSpaceL = SizedBox(width: 24);
  static const Widget horizontalSpaceXL = SizedBox(width: 32);

  /// Const Divider variations
  static const Widget thinDivider = Divider(height: 1, thickness: 0.5);
  static const Widget standardDivider = Divider(height: 1);
  static const Widget thickDivider = Divider(height: 2, thickness: 2);

  /// Const loading indicators
  static const Widget centerLoading =
      Center(child: CircularProgressIndicator());
  static const Widget smallLoading = SizedBox(
    width: 16,
    height: 16,
    child: CircularProgressIndicator(strokeWidth: 2),
  );
}
