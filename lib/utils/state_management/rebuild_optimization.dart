import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// State management optimization utilities for Dasso Reader
///
/// This system provides tools to minimize widget rebuilds while respecting
/// the existing Riverpod implementation patterns in the app.
class RebuildOptimization {
  /// Debug flag to enable rebuild tracking in development
  static const bool _debugRebuilds = kDebugMode;

  /// Track widget rebuilds for performance monitoring
  static final Map<String, int> _rebuildCounts = {};

  /// Log widget rebuild for debugging purposes
  static void logRebuild(String widgetName) {
    if (_debugRebuilds) {
      _rebuildCounts[widgetName] = (_rebuildCounts[widgetName] ?? 0) + 1;
      debugPrint(
          '🔄 Rebuild: $widgetName (${_rebuildCounts[widgetName]} times)');
    }
  }

  /// Get rebuild statistics for performance analysis
  static Map<String, int> getRebuildStats() => Map.from(_rebuildCounts);

  /// Clear rebuild statistics
  static void clearRebuildStats() => _rebuildCounts.clear();

  /// Print rebuild statistics summary
  static void printRebuildSummary() {
    if (_debugRebuilds && _rebuildCounts.isNotEmpty) {
      debugPrint('\n📊 Widget Rebuild Summary:');
      final sortedEntries = _rebuildCounts.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      for (final entry in sortedEntries) {
        debugPrint('  ${entry.key}: ${entry.value} rebuilds');
      }
      debugPrint('');
    }
  }
}

/// Enhanced Consumer widget that provides rebuild optimization
///
/// This widget wraps the standard Riverpod Consumer with additional
/// optimization features while maintaining full compatibility.
class OptimizedConsumer<T> extends ConsumerWidget {
  const OptimizedConsumer({
    super.key,
    required this.provider,
    required this.builder,
    this.child,
    this.debugName,
    this.shouldRebuild,
  });

  final ProviderListenable<T> provider;
  final Widget Function(BuildContext context, T value, Widget? child) builder;
  final Widget? child;
  final String? debugName;
  final bool Function(T previous, T current)? shouldRebuild;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final name = debugName ?? 'OptimizedConsumer<$T>';
    RebuildOptimization.logRebuild(name);

    // Use ref.watch with optional rebuild condition
    final value = ref.watch(provider);

    return builder(context, value, child);
  }
}

/// Selector-like widget for Riverpod that only rebuilds when selected data changes
///
/// This provides similar functionality to Provider's Selector but for Riverpod,
/// allowing fine-grained control over when widgets rebuild.
class RiverpodSelector<T, R> extends ConsumerWidget {
  const RiverpodSelector({
    super.key,
    required this.provider,
    required this.selector,
    required this.builder,
    this.child,
    this.debugName,
  });

  final ProviderListenable<T> provider;
  final R Function(T value) selector;
  final Widget Function(BuildContext context, R value, Widget? child) builder;
  final Widget? child;
  final String? debugName;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final name = debugName ?? 'RiverpodSelector<$T, $R>';
    RebuildOptimization.logRebuild(name);

    // Watch the provider and select only the needed data
    final fullValue = ref.watch(provider);
    final selectedValue = selector(fullValue);

    return builder(context, selectedValue, child);
  }
}

/// Async selector for FutureProvider and StreamProvider
class AsyncRiverpodSelector<T, R> extends ConsumerWidget {
  const AsyncRiverpodSelector({
    super.key,
    required this.provider,
    required this.selector,
    required this.builder,
    this.child,
    this.debugName,
    this.loading,
    this.error,
  });

  final ProviderListenable<AsyncValue<T>> provider;
  final R Function(T value) selector;
  final Widget Function(BuildContext context, R value, Widget? child) builder;
  final Widget? child;
  final String? debugName;
  final Widget Function(BuildContext context)? loading;
  final Widget Function(
      BuildContext context, Object error, StackTrace stackTrace)? error;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final name = debugName ?? 'AsyncRiverpodSelector<$T, $R>';
    RebuildOptimization.logRebuild(name);

    final asyncValue = ref.watch(provider);

    return asyncValue.when(
      data: (data) {
        final selectedValue = selector(data);
        return builder(context, selectedValue, child);
      },
      loading: () =>
          loading?.call(context) ?? const CircularProgressIndicator(),
      error: (err, stack) =>
          error?.call(context, err, stack) ?? Text('Error: $err'),
    );
  }
}

/// Mixin for StatefulWidget to track rebuilds and provide optimization utilities
mixin RebuildOptimizationMixin<T extends StatefulWidget> on State<T> {
  String get debugName => widget.runtimeType.toString();

  @override
  Widget build(BuildContext context) {
    RebuildOptimization.logRebuild(debugName);
    return buildOptimized(context);
  }

  /// Override this method instead of build() when using the mixin
  Widget buildOptimized(BuildContext context);
}

/// Const widget wrapper that ensures a widget is const when possible
class ConstWrapper extends StatelessWidget {
  const ConstWrapper({
    super.key,
    required this.child,
    this.debugName,
  });

  final Widget child;
  final String? debugName;

  @override
  Widget build(BuildContext context) {
    if (kDebugMode && debugName != null) {
      RebuildOptimization.logRebuild('ConstWrapper($debugName)');
    }
    return child;
  }
}

/// Extension methods for common optimization patterns
extension WidgetOptimizationExtensions on Widget {
  /// Wrap widget with RepaintBoundary for expensive widgets
  Widget withRepaintBoundary({String? debugName}) {
    return RepaintBoundary(
      child: kDebugMode && debugName != null
          ? ConstWrapper(debugName: 'RepaintBoundary($debugName)', child: this)
          : this,
    );
  }

  /// Wrap widget with const wrapper for debugging
  Widget withConstWrapper(String debugName) {
    return kDebugMode ? ConstWrapper(debugName: debugName, child: this) : this;
  }

  /// Add automatic keep alive for expensive widgets in lists
  Widget withKeepAlive() {
    return _KeepAliveWrapper(child: this);
  }
}

/// Internal widget to provide keep alive functionality
class _KeepAliveWrapper extends StatefulWidget {
  const _KeepAliveWrapper({required this.child});

  final Widget child;

  @override
  State<_KeepAliveWrapper> createState() => _KeepAliveWrapperState();
}

class _KeepAliveWrapperState extends State<_KeepAliveWrapper>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }
}

/// Performance monitoring widget for development
class PerformanceMonitor extends StatefulWidget {
  const PerformanceMonitor({
    super.key,
    required this.child,
    this.enabled = kDebugMode,
  });

  final Widget child;
  final bool enabled;

  @override
  State<PerformanceMonitor> createState() => _PerformanceMonitorState();
}

class _PerformanceMonitorState extends State<PerformanceMonitor> {
  @override
  void initState() {
    super.initState();
    if (widget.enabled) {
      // Print rebuild summary periodically in debug mode
      Future.delayed(const Duration(seconds: 10), () {
        if (mounted) {
          RebuildOptimization.printRebuildSummary();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
