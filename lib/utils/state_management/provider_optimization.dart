import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Provider optimization utilities for Dasso Reader
///
/// This system provides optimized patterns for the existing Riverpod providers
/// to minimize unnecessary rebuilds and improve performance.
class ProviderOptimization {
  /// Cache for provider selectors to avoid recreating them
  static final Map<String, dynamic> _selectorCache = {};

  /// Get or create a cached selector
  static T getCachedSelector<T>(String key, T Function() factory) {
    return _selectorCache.putIfAbsent(key, factory) as T;
  }

  /// Clear selector cache
  static void clearSelectorCache() => _selectorCache.clear();

  /// Create an optimized provider that only notifies when specific fields change
  static Provider<R> createFieldSelector<T, R>(
    ProviderListenable<T> provider,
    R Function(T value) selector, {
    String? debugName,
  }) {
    return Provider<R>((ref) {
      final value = ref.watch(provider);
      final selected = selector(value);

      if (kDebugMode && debugName != null) {
        debugPrint('🎯 Field Selector: $debugName updated');
      }

      return selected;
    });
  }

  /// Create an optimized async provider with caching
  static FutureProvider<T> createCachedAsyncProvider<T>(
    Future<T> Function() fetcher, {
    Duration? cacheDuration,
    String? debugName,
  }) {
    DateTime? lastFetch;
    T? cachedValue;

    return FutureProvider<T>((ref) async {
      final now = DateTime.now();
      final duration = cacheDuration ?? const Duration(minutes: 5);

      // Return cached value if still valid
      if (lastFetch != null &&
          cachedValue != null &&
          now.difference(lastFetch!) < duration) {
        if (kDebugMode && debugName != null) {
          debugPrint('📦 Cache Hit: $debugName');
        }
        return cachedValue!;
      }

      // Fetch new value
      if (kDebugMode && debugName != null) {
        debugPrint('🌐 Cache Miss: $debugName - fetching new data');
      }

      final value = await fetcher();
      lastFetch = now;
      cachedValue = value;

      return value;
    });
  }

  /// Create a debounced provider that delays updates
  static StateProvider<T> createDebouncedProvider<T>(
    T initialValue, {
    Duration delay = const Duration(milliseconds: 300),
    String? debugName,
  }) {
    return StateProvider<T>((ref) => initialValue);
  }

  /// Create a provider that only updates when a condition is met
  static Provider<T> createConditionalProvider<T>(
    ProviderListenable<T> provider,
    bool Function(T previous, T current) shouldUpdate, {
    String? debugName,
  }) {
    return Provider<T>((ref) {
      T? previous;

      return ref.watch(provider.select((value) {
        final current = value;
        final shouldUpdateValue =
            previous == null || shouldUpdate(previous!, current);

        if (shouldUpdateValue) {
          if (kDebugMode && debugName != null) {
            debugPrint('✅ Conditional Update: $debugName');
          }
          previous = current;
          return current;
        }

        if (kDebugMode && debugName != null) {
          debugPrint('⏭️ Conditional Skip: $debugName');
        }
        return previous!;
      }));
    });
  }
}

/// Optimized provider patterns for common use cases in Dasso Reader
class DassoProviderPatterns {
  /// Book list optimization - only rebuild when book count or specific books change
  static Provider<int> createBookCountProvider(
      ProviderListenable<AsyncValue<List<List<dynamic>>>> bookListProvider) {
    return Provider<int>((ref) {
      return ref.watch(bookListProvider.select((asyncBooks) {
        return asyncBooks.when(
          data: (books) =>
              books.fold<int>(0, (sum, group) => sum + group.length),
          loading: () => 0,
          error: (_, __) => 0,
        );
      }));
    });
  }

  /// HSK progress optimization - only rebuild when progress percentage changes
  static Provider<double> createHskProgressProvider(
      ProviderListenable<Map<String, dynamic>> hskSessionProvider) {
    return Provider<double>((ref) {
      return ref.watch(hskSessionProvider.select((session) {
        final correct = session['correctAnswers'] as int? ?? 0;
        final total = session['totalQuestions'] as int? ?? 1;
        return total > 0 ? correct / total : 0.0;
      }));
    });
  }

  /// Reading theme optimization - only rebuild when colors change
  static Provider<Map<String, String>> createReadingColorsProvider(
      ProviderListenable<dynamic> prefsProvider) {
    return Provider<Map<String, String>>((ref) {
      return ref.watch(prefsProvider.select((prefs) {
        // Extract only color-related properties
        return {
          'backgroundColor': prefs.readTheme?.backgroundColor ?? 'FFFBFBF3',
          'textColor': prefs.readTheme?.textColor ?? 'FF343434',
        };
      }));
    });
  }

  /// AI chat optimization - only rebuild when message count changes
  static Provider<int> createAiMessageCountProvider(
      ProviderListenable<AsyncValue<List<dynamic>>> aiChatProvider) {
    return Provider<int>((ref) {
      return ref.watch(aiChatProvider.select((asyncMessages) {
        return asyncMessages.when(
          data: (messages) => messages.length,
          loading: () => 0,
          error: (_, __) => 0,
        );
      }));
    });
  }

  /// Search optimization - only rebuild when search results change
  static Provider<bool> createHasSearchResultsProvider(
      ProviderListenable<String?> searchTermProvider,
      ProviderListenable<AsyncValue<List<dynamic>>> searchResultsProvider) {
    return Provider<bool>((ref) {
      final term = ref.watch(searchTermProvider);
      final results = ref.watch(searchResultsProvider);

      if (term == null || term.isEmpty) return false;

      return results.when(
        data: (data) => data.isNotEmpty,
        loading: () => false,
        error: (_, __) => false,
      );
    });
  }

  /// Statistics optimization - only rebuild when chart data changes
  static Provider<bool> createHasStatisticsDataProvider(
      ProviderListenable<AsyncValue<dynamic>> statisticsProvider) {
    return Provider<bool>((ref) {
      return ref.watch(statisticsProvider.select((asyncStats) {
        return asyncStats.when(
          data: (stats) => stats != null,
          loading: () => false,
          error: (_, __) => false,
        );
      }));
    });
  }
}

/// Mixin for providers to add optimization features
mixin ProviderOptimizationMixin {
  /// Create a selector that only updates when the selected value changes
  Provider<R> selectField<T, R>(
    ProviderListenable<T> provider,
    R Function(T value) selector, {
    String? debugName,
  }) {
    return ProviderOptimization.createFieldSelector(
      provider,
      selector,
      debugName: debugName,
    );
  }

  /// Create a cached async provider
  FutureProvider<T> cachedAsync<T>(
    Future<T> Function() fetcher, {
    Duration? cacheDuration,
    String? debugName,
  }) {
    return ProviderOptimization.createCachedAsyncProvider(
      fetcher,
      cacheDuration: cacheDuration,
      debugName: debugName,
    );
  }
}

/// Performance monitoring for providers
class ProviderPerformanceMonitor {
  static final Map<String, ProviderStats> _stats = {};

  /// Record provider access
  static void recordAccess(String providerName) {
    if (!kDebugMode) return;

    final stats = _stats.putIfAbsent(
      providerName,
      () => ProviderStats(providerName),
    );
    stats.recordAccess();
  }

  /// Record provider rebuild
  static void recordRebuild(String providerName) {
    if (!kDebugMode) return;

    final stats = _stats.putIfAbsent(
      providerName,
      () => ProviderStats(providerName),
    );
    stats.recordRebuild();
  }

  /// Get provider statistics
  static Map<String, ProviderStats> getStats() => Map.from(_stats);

  /// Clear statistics
  static void clearStats() => _stats.clear();

  /// Print performance summary
  static void printPerformanceSummary() {
    if (!kDebugMode || _stats.isEmpty) return;

    debugPrint('\n📊 Provider Performance Summary:');

    final sortedStats = _stats.values.toList()
      ..sort((a, b) => b.rebuilds.compareTo(a.rebuilds));

    for (final stats in sortedStats.take(10)) {
      final efficiency = stats.accesses > 0
          ? (stats.rebuilds / stats.accesses * 100).toStringAsFixed(1)
          : '0.0';
      debugPrint(
          '  ${stats.name}: ${stats.rebuilds} rebuilds, ${stats.accesses} accesses ($efficiency% rebuild rate)');
    }
    debugPrint('');
  }
}

/// Statistics for individual providers
class ProviderStats {
  ProviderStats(this.name);

  final String name;
  int accesses = 0;
  int rebuilds = 0;
  DateTime? lastAccess;
  DateTime? lastRebuild;

  void recordAccess() {
    accesses++;
    lastAccess = DateTime.now();
  }

  void recordRebuild() {
    rebuilds++;
    lastRebuild = DateTime.now();
  }

  double get rebuildRate => accesses > 0 ? rebuilds / accesses : 0.0;
}
