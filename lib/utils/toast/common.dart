import 'package:dasso_reader/config/color_system.dart';
import 'package:dasso_reader/main.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class AnxToast {
  static FToast fToast = FToast();

  static void init(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      fToast.init(context);
    });
  }

  static void show(String message,
      {Icon? icon, int duration = 2000, StatusType type = StatusType.neutral}) {
    final context = navigatorKey.currentContext!;
    final statusColors = ColorSystem.getStatusColors(context, type);

    Widget toast = Container(
      // width: MediaQuery.of(navigatorKey.currentContext!).size.width * 0.1,
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25.0),
        color: statusColors.background,
        border: Border.all(color: statusColors.border, width: 1.0),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon?.icon ?? _getDefaultIcon(type),
            color: statusColors.icon,
            size: icon?.size ?? 20.0,
          ),
          const SizedBox(
            width: 12.0,
          ),
          Flexible(
            child: Text(
              message,
              // wrap
              style: TextStyle(
                color: statusColors.foreground,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );

    // close previous toast
    fToast.removeQueuedCustomToasts();

    fToast.showToast(
      child: toast,
      gravity: ToastGravity.BOTTOM,
      toastDuration: Duration(milliseconds: duration),
    );

    // // Custom Toast Position
    // fToast.showToast(
    //     child: toast,
    //     toastDuration: Duration(seconds: 2),
    //     positionedToastBuilder: (context, child) {
    //       return Positioned(
    //         child: child,
    //         top: 16.0,
    //         left: 16.0,
    //       );
    //     });
  }

  /// Get default icon based on status type
  static IconData _getDefaultIcon(StatusType type) {
    switch (type) {
      case StatusType.info:
        return Icons.info_outline;
      case StatusType.success:
        return Icons.check_circle_outline;
      case StatusType.warning:
        return Icons.warning_amber_outlined;
      case StatusType.error:
        return Icons.error_outline;
      case StatusType.neutral:
        return Icons.info_outline;
    }
  }

  /// Convenience methods for different toast types
  static void showSuccess(String message, {int duration = 2000}) {
    show(message, type: StatusType.success, duration: duration);
  }

  static void showError(String message, {int duration = 3000}) {
    show(message, type: StatusType.error, duration: duration);
  }

  static void showWarning(String message, {int duration = 2500}) {
    show(message, type: StatusType.warning, duration: duration);
  }

  static void showInfo(String message, {int duration = 2000}) {
    show(message, type: StatusType.info, duration: duration);
  }
}
