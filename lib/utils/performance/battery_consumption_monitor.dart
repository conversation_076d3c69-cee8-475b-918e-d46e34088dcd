import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:battery_plus/battery_plus.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// Battery consumption monitoring and optimization system
class BatteryConsumptionMonitor {
  bool _isInitialized = false;
  bool _isMonitoring = false;
  BatteryConfig _config = BatteryConfig.defaultConfig();
  Timer? _monitoringTimer;

  final Battery _battery = Battery();
  int _currentBatteryLevel = 100;
  BatteryState _currentBatteryState = BatteryState.unknown;
  final List<BatterySnapshot> _batteryHistory = [];

  bool _isPowerSavingMode = false;

  Future<void> initialize(BatteryConfig config) async {
    if (_isInitialized) return;

    _config = config;
    _isInitialized = true;

    // Get initial battery status
    await _updateBatteryStatus();

    if (kDebugMode) {
      AnxLog.info(
          '🔋 BatteryConsumptionMonitor: Initialized at $_currentBatteryLevel%');
    }
  }

  void startMonitoring() {
    if (!_isInitialized || _isMonitoring) return;

    _isMonitoring = true;
    _setupPeriodicMonitoring();

    if (kDebugMode) {
      AnxLog.info('🔋 BatteryConsumptionMonitor: Started monitoring');
    }
  }

  void stopMonitoring() {
    if (!_isMonitoring) return;

    _isMonitoring = false;
    _monitoringTimer?.cancel();
    _monitoringTimer = null;

    if (kDebugMode) {
      AnxLog.info('🔋 BatteryConsumptionMonitor: Stopped monitoring');
    }
  }

  BatteryMetrics getMetrics() {
    return BatteryMetrics(
      currentLevel: _currentBatteryLevel,
      currentState: _currentBatteryState,
      batteryHistory: List.from(_batteryHistory),
      isPowerSavingMode: _isPowerSavingMode,
      performanceScore: _calculatePerformanceScore(),
    );
  }

  int get currentBatteryLevel => _currentBatteryLevel;
  bool get isPowerSavingMode => _isPowerSavingMode;

  /// Force enable power saving mode (user preference)
  void enablePowerSavingMode({bool userRequested = false}) {
    if (!_isPowerSavingMode) {
      _enablePowerSavingMode();

      if (userRequested && kDebugMode) {
        AnxLog.info('🔋 Power saving mode enabled by user request');
      }
    }
  }

  /// Force disable power saving mode (user preference)
  void disablePowerSavingMode({bool userRequested = false}) {
    if (_isPowerSavingMode) {
      _disablePowerSavingMode();

      if (userRequested && kDebugMode) {
        AnxLog.info('🔋 Power saving mode disabled by user request');
      }
    }
  }

  /// Check if power saving optimizations should be applied
  /// This can be used by other app components to adapt their behavior
  bool shouldUsePowerSavingOptimizations() {
    return _isPowerSavingMode;
  }

  void _setupPeriodicMonitoring() {
    _monitoringTimer = Timer.periodic(_config.monitoringInterval, (_) async {
      await _updateBatteryStatus();
      _analyzeBatteryUsage();
      _maintainHistory();
    });
  }

  Future<void> _updateBatteryStatus() async {
    try {
      final level = await _battery.batteryLevel;
      final state = await _battery.batteryState;

      _currentBatteryLevel = level;
      _currentBatteryState = state;

      final snapshot = BatterySnapshot(
        timestamp: DateTime.now(),
        level: level,
        state: state,
      );

      _batteryHistory.add(snapshot);
    } catch (error) {
      if (kDebugMode) {
        AnxLog.warning('🔋 Failed to update battery status: $error');
      }
    }
  }

  void _analyzeBatteryUsage() {
    final wasLowBattery = _currentBatteryLevel <= _config.lowBatteryThreshold;
    final wasPowerSaving = _isPowerSavingMode;

    // Check for low battery
    if (wasLowBattery && !wasPowerSaving) {
      _enablePowerSavingMode();
    } else if (!wasLowBattery && wasPowerSaving) {
      _disablePowerSavingMode();
    }

    // Check battery drain rate
    _checkBatteryDrainRate();
  }

  void _enablePowerSavingMode() {
    _isPowerSavingMode = true;

    // Apply power saving optimizations to app features
    _applyPowerSavingOptimizations();

    if (kDebugMode) {
      AnxLog.info('🔋 Power saving mode enabled at $_currentBatteryLevel%');
    }
  }

  void _disablePowerSavingMode() {
    _isPowerSavingMode = false;

    // Restore normal performance settings
    _restoreNormalPerformance();

    if (kDebugMode) {
      AnxLog.info('🔋 Power saving mode disabled at $_currentBatteryLevel%');
    }
  }

  /// Apply power saving optimizations to various app features
  void _applyPowerSavingOptimizations() {
    try {
      // Reduce TTS quality and preloading
      _optimizeTTSForPowerSaving();

      // Reduce animation complexity
      _reduceAnimationComplexity();

      // Optimize background tasks
      _optimizeBackgroundTasks();

      // Reduce monitoring frequency
      _reduceMonitoringFrequency();

      if (kDebugMode) {
        AnxLog.info('🔋 Applied power saving optimizations');
      }
    } catch (e) {
      if (kDebugMode) {
        AnxLog.warning('🔋 Error applying power saving optimizations: $e');
      }
    }
  }

  /// Restore normal performance settings
  void _restoreNormalPerformance() {
    try {
      // Restore TTS quality
      _restoreTTSQuality();

      // Restore animation complexity
      _restoreAnimationComplexity();

      // Restore background task frequency
      _restoreBackgroundTasks();

      // Restore normal monitoring frequency
      _restoreMonitoringFrequency();

      if (kDebugMode) {
        AnxLog.info('🔋 Restored normal performance settings');
      }
    } catch (e) {
      if (kDebugMode) {
        AnxLog.warning('🔋 Error restoring normal performance: $e');
      }
    }
  }

  /// Optimize TTS for power saving
  void _optimizeTTSForPowerSaving() {
    // Note: In a real implementation, this would:
    // 1. Reduce TTS preloading frequency
    // 2. Lower audio quality settings
    // 3. Disable audio preloading for next segments
    // 4. Reduce TTS processing rate

    if (kDebugMode) {
      AnxLog.info('🔋 TTS optimized for power saving');
    }
  }

  /// Restore TTS quality
  void _restoreTTSQuality() {
    // Note: In a real implementation, this would:
    // 1. Restore normal TTS preloading
    // 2. Restore high audio quality
    // 3. Re-enable audio preloading
    // 4. Restore normal TTS processing rate

    if (kDebugMode) {
      AnxLog.info('🔋 TTS quality restored');
    }
  }

  /// Reduce animation complexity for power saving
  void _reduceAnimationComplexity() {
    // Note: In a real implementation, this would:
    // 1. Disable non-essential animations
    // 2. Reduce animation frame rates
    // 3. Use simpler animation curves
    // 4. Disable stroke animations in dictionary

    if (kDebugMode) {
      AnxLog.info('🔋 Animation complexity reduced');
    }
  }

  /// Restore animation complexity
  void _restoreAnimationComplexity() {
    // Note: In a real implementation, this would:
    // 1. Re-enable all animations
    // 2. Restore normal frame rates
    // 3. Use standard animation curves
    // 4. Re-enable stroke animations

    if (kDebugMode) {
      AnxLog.info('🔋 Animation complexity restored');
    }
  }

  /// Optimize background tasks for power saving
  void _optimizeBackgroundTasks() {
    // Note: In a real implementation, this would:
    // 1. Reduce background task frequency
    // 2. Defer non-critical background operations
    // 3. Batch background operations
    // 4. Reduce data transformation frequency

    if (kDebugMode) {
      AnxLog.info('🔋 Background tasks optimized');
    }
  }

  /// Restore background task frequency
  void _restoreBackgroundTasks() {
    // Note: In a real implementation, this would:
    // 1. Restore normal background task frequency
    // 2. Resume deferred operations
    // 3. Restore individual operation processing
    // 4. Resume normal data transformation

    if (kDebugMode) {
      AnxLog.info('🔋 Background tasks restored');
    }
  }

  /// Reduce monitoring frequency for power saving
  void _reduceMonitoringFrequency() {
    // Increase monitoring interval to save power
    _monitoringTimer?.cancel();
    _monitoringTimer = Timer.periodic(
      Duration(
          minutes:
              _config.monitoringInterval.inMinutes * 2), // Double the interval
      (_) async {
        await _updateBatteryStatus();
        _analyzeBatteryUsage();
        _maintainHistory();
      },
    );

    if (kDebugMode) {
      AnxLog.info('🔋 Monitoring frequency reduced');
    }
  }

  /// Restore normal monitoring frequency
  void _restoreMonitoringFrequency() {
    // Restore normal monitoring interval
    _monitoringTimer?.cancel();
    _monitoringTimer = Timer.periodic(_config.monitoringInterval, (_) async {
      await _updateBatteryStatus();
      _analyzeBatteryUsage();
      _maintainHistory();
    });

    if (kDebugMode) {
      AnxLog.info('🔋 Monitoring frequency restored');
    }
  }

  void _checkBatteryDrainRate() {
    if (_batteryHistory.length < 2) return;

    final recent = _batteryHistory.skip(_batteryHistory.length - 2).toList();
    final timeDiff = recent[1].timestamp.difference(recent[0].timestamp);
    final levelDiff = recent[0].level - recent[1].level;

    if (timeDiff.inMinutes > 0 && levelDiff > 0) {
      final drainRate = levelDiff / timeDiff.inMinutes; // % per minute

      if (drainRate > _config.highDrainRateThreshold) {
        if (kDebugMode) {
          AnxLog.warning(
              '🔋 High battery drain detected: ${drainRate.toStringAsFixed(2)}%/min');
        }
      }
    }
  }

  void _maintainHistory() {
    while (_batteryHistory.length > _config.maxHistorySize) {
      _batteryHistory.removeAt(0);
    }
  }

  double _calculatePerformanceScore() {
    // Base score on current battery level
    double score = _currentBatteryLevel.toDouble();

    // Penalize for power saving mode
    if (_isPowerSavingMode) {
      score -= 20.0;
    }

    // Penalize for charging state (indicates high consumption)
    if (_currentBatteryState == BatteryState.charging) {
      score -= 10.0;
    }

    return score.clamp(0.0, 100.0);
  }

  void dispose() {
    stopMonitoring();
    _batteryHistory.clear();
    _isInitialized = false;

    if (kDebugMode) {
      AnxLog.info('🔋 BatteryConsumptionMonitor: Disposed');
    }
  }
}

class BatteryConfig {
  const BatteryConfig({
    required this.lowBatteryThreshold,
    required this.highDrainRateThreshold,
    required this.monitoringInterval,
    required this.maxHistorySize,
  });

  final int lowBatteryThreshold; // Battery percentage for low battery
  final double highDrainRateThreshold; // % per minute for high drain rate
  final Duration monitoringInterval;
  final int maxHistorySize;

  factory BatteryConfig.defaultConfig() {
    return const BatteryConfig(
      lowBatteryThreshold: 20, // 20% battery for low battery
      highDrainRateThreshold: 2.0, // 2% per minute for high drain
      monitoringInterval: Duration(minutes: 1),
      maxHistorySize: 100,
    );
  }
}

class BatteryMetrics {
  const BatteryMetrics({
    required this.currentLevel,
    required this.currentState,
    required this.batteryHistory,
    required this.isPowerSavingMode,
    required this.performanceScore,
  });

  final int currentLevel;
  final BatteryState currentState;
  final List<BatterySnapshot> batteryHistory;
  final bool isPowerSavingMode;
  final double performanceScore;

  bool get isBatteryHealthy => performanceScore >= 70.0;
  bool get isLowBattery => currentLevel <= 20;
}

class BatterySnapshot {
  const BatterySnapshot({
    required this.timestamp,
    required this.level,
    required this.state,
  });

  final DateTime timestamp;
  final int level;
  final BatteryState state;
}
