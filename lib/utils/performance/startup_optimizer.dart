import 'dart:async';
import 'dart:isolate';
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// Startup optimization system to reduce jank during app initialization
class StartupOptimizer {
  static final StartupOptimizer _instance = StartupOptimizer._internal();
  factory StartupOptimizer() => _instance;
  StartupOptimizer._internal();

  final List<StartupTask> _tasks = [];
  final List<StartupTask> _completedTasks = [];
  bool _isOptimizing = false;
  DateTime? _startTime;

  /// Add a task to be optimized during startup
  void addTask(StartupTask task) {
    if (_isOptimizing) {
      AnxLog.warning('Cannot add task during optimization: ${task.name}');
      return;
    }
    _tasks.add(task);
  }

  /// Start the optimization process
  Future<void> optimize() async {
    if (_isOptimizing) return;

    _isOptimizing = true;
    _startTime = DateTime.now();

    if (kDebugMode) {
      AnxLog.info(
          '🚀 StartupOptimizer: Beginning optimization with ${_tasks.length} tasks');
    }

    try {
      // Sort tasks by priority (higher priority first)
      _tasks.sort((a, b) => b.priority.compareTo(a.priority));

      // Execute critical tasks immediately
      await _executeCriticalTasks();

      // Schedule non-critical tasks for later execution
      _scheduleNonCriticalTasks();

      if (kDebugMode) {
        final duration = DateTime.now().difference(_startTime!);
        AnxLog.info(
            '🚀 StartupOptimizer: Critical tasks completed in ${duration.inMilliseconds}ms');
      }
    } catch (e) {
      AnxLog.severe('StartupOptimizer error: $e');
    } finally {
      _isOptimizing = false;
    }
  }

  /// Execute critical tasks that must complete before UI is shown
  Future<void> _executeCriticalTasks() async {
    final criticalTasks =
        _tasks.where((task) => task.priority >= TaskPriority.critical).toList();

    for (final task in criticalTasks) {
      await _executeTask(task);
    }
  }

  /// Schedule non-critical tasks to run after the UI is ready
  void _scheduleNonCriticalTasks() {
    final nonCriticalTasks =
        _tasks.where((task) => task.priority < TaskPriority.critical).toList();

    if (nonCriticalTasks.isEmpty) return;

    // Use post-frame callback to ensure UI is ready
    SchedulerBinding.instance.addPostFrameCallback((_) {
      _executeNonCriticalTasksAsync(nonCriticalTasks);
    });
  }

  /// Execute non-critical tasks asynchronously
  Future<void> _executeNonCriticalTasksAsync(List<StartupTask> tasks) async {
    for (final task in tasks) {
      // Yield control between tasks to prevent jank
      await Future.delayed(Duration.zero);

      if (task.canRunInBackground) {
        // Run heavy tasks in isolate if possible
        await _executeTaskInIsolate(task);
      } else {
        await _executeTask(task);
      }
    }
  }

  /// Execute a single task with timing and error handling
  Future<void> _executeTask(StartupTask task) async {
    final stopwatch = Stopwatch()..start();

    try {
      if (kDebugMode) {
        AnxLog.info('🔄 Executing startup task: ${task.name}');
      }

      await task.execute();

      stopwatch.stop();
      _completedTasks.add(task);

      if (kDebugMode) {
        AnxLog.info(
            '✅ Completed startup task: ${task.name} (${stopwatch.elapsedMilliseconds}ms)');
      }

      // Log warning for slow tasks
      if (stopwatch.elapsedMilliseconds > 100) {
        AnxLog.warning(
            '⚠️ Slow startup task: ${task.name} took ${stopwatch.elapsedMilliseconds}ms');
      }
    } catch (e) {
      stopwatch.stop();
      AnxLog.severe('❌ Startup task failed: ${task.name} - $e');

      // Continue with other tasks even if one fails
    }
  }

  /// Execute task in isolate for heavy operations
  Future<void> _executeTaskInIsolate(StartupTask task) async {
    if (!task.canRunInBackground) return;

    final ReceivePort receivePort = ReceivePort();
    final Completer<void> taskCompleter = Completer<void>();
    SendPort? isolateSendPort;

    try {
      // Set up timeout for isolate communication
      Timer? timeoutTimer = Timer(const Duration(seconds: 30), () {
        if (!taskCompleter.isCompleted) {
          AnxLog.warning(
              'Isolate task ${task.name} timed out, falling back to main thread');
          taskCompleter.complete();
        }
      });

      // Listen for messages from isolate
      receivePort.listen((message) {
        try {
          final Map<String, dynamic> data = message as Map<String, dynamic>;
          final String messageType = data['type'] as String;

          switch (messageType) {
            case 'isolate_ready':
              // Store the isolate's send port for communication
              isolateSendPort = data['receivePort'] as SendPort;

              // Send the task to execute
              isolateSendPort?.send({
                'type': 'execute_task',
                'taskType': _getTaskType(task),
                'taskId': task.name,
                'params': task.isolateData ?? {},
              });
              break;

            case 'progress':
              // Handle progress updates
              final double progress = data['progress'] as double;
              final String status = data['status'] as String;
              final String? message = data['message'] as String?;

              if (kDebugMode) {
                AnxLog.info(
                    'Task ${task.name} progress: ${(progress * 100).toInt()}% - $status${message != null ? ': $message' : ''}');
              }
              break;

            case 'completed':
              // Task completed successfully
              timeoutTimer.cancel();
              if (!taskCompleter.isCompleted) {
                taskCompleter.complete();
              }
              break;

            case 'error':
              // Task failed in isolate
              final String error = data['message'] as String;
              AnxLog.warning('Isolate task ${task.name} failed: $error');
              timeoutTimer.cancel();
              if (!taskCompleter.isCompleted) {
                taskCompleter.complete();
              }
              break;
          }
        } catch (e) {
          AnxLog.warning(
              'Error processing isolate message for ${task.name}: $e');
        }
      });

      // Spawn the isolate
      await Isolate.spawn(_isolateEntryPoint, {
        'sendPort': receivePort.sendPort,
      });

      // Wait for task completion or timeout
      await taskCompleter.future;

      // Cleanup
      timeoutTimer.cancel();
      receivePort.close();

      // Send shutdown signal to isolate
      isolateSendPort?.send({'type': 'shutdown'});
    } catch (e) {
      AnxLog.warning('Failed to spawn isolate for ${task.name}: $e');
      // Fallback to main thread execution
      await _executeTask(task);
    }
  }

  /// Get task type string for isolate communication
  String _getTaskType(StartupTask task) {
    if (task is DictionaryLoadTask) {
      return 'dictionary_load';
    } else if (task is DatabaseInitTask) {
      return 'database_init';
    } else {
      return 'generic';
    }
  }

  /// Entry point for isolate execution
  static void _isolateEntryPoint(Map<String, dynamic> data) {
    try {
      // Extract communication ports
      final SendPort sendPort = data['sendPort'] as SendPort;
      final ReceivePort receivePort = ReceivePort();

      // Send back our receive port for bidirectional communication
      sendPort.send({
        'type': 'isolate_ready',
        'receivePort': receivePort.sendPort,
      });

      // Listen for task execution requests
      receivePort.listen((message) async {
        try {
          final Map<String, dynamic> taskData = message as Map<String, dynamic>;
          final String messageType = taskData['type'] as String;

          switch (messageType) {
            case 'execute_task':
              await _executeTaskInIsolateWorker(taskData, sendPort);
              break;
            case 'shutdown':
              receivePort.close();
              break;
            default:
              sendPort.send({
                'type': 'error',
                'message': 'Unknown message type: $messageType',
              });
          }
        } catch (e, stackTrace) {
          sendPort.send({
            'type': 'error',
            'message': 'Error processing message: $e',
            'stackTrace': stackTrace.toString(),
          });
        }
      });
    } catch (e) {
      // If we can't even set up communication, there's not much we can do
      // The main thread will timeout and fall back to main thread execution
      // Use basic logging since AnxLog might not be available in isolate
      // The main thread will timeout and fall back to main thread execution
    }
  }

  /// Execute a task within the isolate
  static Future<void> _executeTaskInIsolateWorker(
    Map<String, dynamic> taskData,
    SendPort sendPort,
  ) async {
    try {
      final String taskType = taskData['taskType'] as String;
      final Map<String, dynamic> taskParams =
          taskData['params'] as Map<String, dynamic>;
      final String taskId = taskData['taskId'] as String;

      // Send progress update - task started
      sendPort.send({
        'type': 'progress',
        'taskId': taskId,
        'status': 'started',
        'progress': 0.0,
      });

      // Execute task based on type
      switch (taskType) {
        case 'dictionary_load':
          await _executeDictionaryLoadInIsolate(taskParams, sendPort, taskId);
          break;
        case 'database_init':
          await _executeDatabaseInitInIsolate(taskParams, sendPort, taskId);
          break;
        default:
          throw UnsupportedError('Unknown task type: $taskType');
      }

      // Send completion notification
      sendPort.send({
        'type': 'completed',
        'taskId': taskId,
        'result': 'success',
      });
    } catch (e, stackTrace) {
      sendPort.send({
        'type': 'error',
        'taskId': taskData['taskId'],
        'message': e.toString(),
        'stackTrace': stackTrace.toString(),
      });
    }
  }

  /// Execute dictionary loading in isolate
  static Future<void> _executeDictionaryLoadInIsolate(
    Map<String, dynamic> params,
    SendPort sendPort,
    String taskId,
  ) async {
    // Simulate chunked dictionary loading
    final int totalChunks = params['totalChunks'] as int? ?? 10;

    for (int i = 0; i < totalChunks; i++) {
      // Simulate chunk processing
      await Future.delayed(const Duration(milliseconds: 100));

      // Send progress update
      final progress = (i + 1) / totalChunks;
      sendPort.send({
        'type': 'progress',
        'taskId': taskId,
        'status': 'processing',
        'progress': progress,
        'message': 'Loaded chunk ${i + 1}/$totalChunks',
      });
    }
  }

  /// Execute database initialization in isolate
  static Future<void> _executeDatabaseInitInIsolate(
    Map<String, dynamic> params,
    SendPort sendPort,
    String taskId,
  ) async {
    // Simulate database initialization steps
    final List<String> steps = [
      'Opening database connection',
      'Running migrations',
      'Creating indexes',
      'Optimizing queries',
      'Finalizing setup',
    ];

    for (int i = 0; i < steps.length; i++) {
      // Simulate step processing
      await Future.delayed(const Duration(milliseconds: 200));

      // Send progress update
      final progress = (i + 1) / steps.length;
      sendPort.send({
        'type': 'progress',
        'taskId': taskId,
        'status': 'processing',
        'progress': progress,
        'message': steps[i],
      });
    }
  }

  /// Get optimization summary
  StartupOptimizationSummary getSummary() {
    final totalDuration = _startTime != null
        ? DateTime.now().difference(_startTime!)
        : Duration.zero;

    return StartupOptimizationSummary(
      totalTasks: _tasks.length,
      completedTasks: _completedTasks.length,
      totalDuration: totalDuration,
      completedTaskDetails: _completedTasks,
    );
  }

  /// Clear all tasks and reset state
  void reset() {
    _tasks.clear();
    _completedTasks.clear();
    _isOptimizing = false;
    _startTime = null;
  }
}

/// Represents a startup task that can be optimized
abstract class StartupTask {
  const StartupTask({
    required this.name,
    required this.priority,
    this.canRunInBackground = false,
    this.isolateData,
  });

  final String name;
  final TaskPriority priority;
  final bool canRunInBackground;
  final Map<String, dynamic>? isolateData;

  /// Execute the task
  Future<void> execute();
}

/// Task priority levels
enum TaskPriority {
  low(1),
  normal(2),
  high(3),
  critical(4);

  const TaskPriority(this.value);
  final int value;

  /// Compare priorities for sorting
  int compareTo(TaskPriority other) => value.compareTo(other.value);

  /// Check if this priority is greater than or equal to another
  bool operator >=(TaskPriority other) => value >= other.value;

  /// Check if this priority is less than another
  bool operator <(TaskPriority other) => value < other.value;
}

/// Summary of startup optimization results
class StartupOptimizationSummary {
  const StartupOptimizationSummary({
    required this.totalTasks,
    required this.completedTasks,
    required this.totalDuration,
    required this.completedTaskDetails,
  });

  final int totalTasks;
  final int completedTasks;
  final Duration totalDuration;
  final List<StartupTask> completedTaskDetails;

  double get completionRate =>
      totalTasks > 0 ? completedTasks / totalTasks : 0.0;
  bool get isComplete => completedTasks == totalTasks;
}

/// Concrete implementation for database initialization
class DatabaseInitTask extends StartupTask {
  const DatabaseInitTask(this.initFunction)
      : super(
          name: 'Database Initialization',
          priority: TaskPriority.critical,
        );

  final Future<void> Function() initFunction;

  @override
  Future<void> execute() async {
    await initFunction();
  }
}

/// Concrete implementation for dictionary loading
class DictionaryLoadTask extends StartupTask {
  const DictionaryLoadTask(this.loadFunction)
      : super(
          name: 'Dictionary Loading',
          priority: TaskPriority.high,
          canRunInBackground: true,
        );

  final Future<void> Function() loadFunction;

  @override
  Future<void> execute() async {
    await loadFunction();
  }
}

/// Concrete implementation for preferences loading
class PreferencesLoadTask extends StartupTask {
  const PreferencesLoadTask(this.loadFunction)
      : super(
          name: 'Preferences Loading',
          priority: TaskPriority.critical,
        );

  final Future<void> Function() loadFunction;

  @override
  Future<void> execute() async {
    await loadFunction();
  }
}
