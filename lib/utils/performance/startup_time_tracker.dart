import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// Startup time tracking and optimization system
///
/// This system provides:
/// - Time to First Frame (TTFF) measurement
/// - Phase-by-phase startup analysis
/// - Bottleneck detection and optimization suggestions
/// - Cold/warm start performance tracking
class StartupTimeTracker {
  bool _isInitialized = false;
  StartupConfig _config = StartupConfig.defaultConfig();

  // Startup timing data
  DateTime? _appStartTime;
  DateTime? _firstFrameTime;
  final Map<String, DateTime> _phaseStartTimes = {};
  final Map<String, Duration> _phaseDurations = {};
  final List<StartupPhase> _phases = [];

  // Performance metrics
  Duration? _totalStartupTime;
  Duration? _timeToFirstFrame;
  StartupType _startupType = StartupType.cold;

  // Bottleneck detection
  final List<StartupBottleneck> _bottlenecks = [];

  /// Initialize the startup time tracker
  Future<void> initialize(StartupConfig config) async {
    if (_isInitialized) return;

    _config = config;
    _appStartTime = DateTime.now();
    _isInitialized = true;

    // Track initialization phase
    trackPhase('initialization');

    if (kDebugMode) {
      AnxLog.info(
          '⏱️ StartupTimeTracker: Initialized at ${_appStartTime!.toIso8601String()}');
    }
  }

  /// Track a startup phase
  void trackPhase(String phaseName) {
    if (!_isInitialized) return;

    final now = DateTime.now();

    // End previous phase if exists
    if (_phases.isNotEmpty && !_phases.last.isCompleted) {
      _endPhase(_phases.last.name);
    }

    // Start new phase
    _phaseStartTimes[phaseName] = now;

    final phase = StartupPhase(
      name: phaseName,
      startTime: now,
      order: _phases.length,
    );

    _phases.add(phase);

    if (kDebugMode && _config.enablePhaseLogging) {
      final elapsed = _appStartTime != null
          ? now.difference(_appStartTime!).inMilliseconds
          : 0;
      AnxLog.info('⏱️ Startup Phase: $phaseName started (+${elapsed}ms)');
    }
  }

  /// End a startup phase
  void _endPhase(String phaseName) {
    final startTime = _phaseStartTimes[phaseName];
    if (startTime == null) return;

    final now = DateTime.now();
    final duration = now.difference(startTime);

    _phaseDurations[phaseName] = duration;

    // Update phase in list
    final phaseIndex = _phases.indexWhere((p) => p.name == phaseName);
    if (phaseIndex != -1) {
      _phases[phaseIndex] = _phases[phaseIndex].copyWith(
        endTime: now,
        duration: duration,
      );
    }

    // Check for bottlenecks
    _checkForBottleneck(phaseName, duration);

    if (kDebugMode && _config.enablePhaseLogging) {
      AnxLog.info(
          '⏱️ Startup Phase: $phaseName completed (${duration.inMilliseconds}ms)');
    }
  }

  /// Mark first frame rendered
  void markFirstFrame() {
    if (_firstFrameTime != null) return;

    _firstFrameTime = DateTime.now();

    if (_appStartTime != null) {
      _timeToFirstFrame = _firstFrameTime!.difference(_appStartTime!);

      if (kDebugMode) {
        AnxLog.info('🎯 First Frame: ${_timeToFirstFrame!.inMilliseconds}ms');
      }
    }

    // End any remaining phases
    if (_phases.isNotEmpty && !_phases.last.isCompleted) {
      _endPhase(_phases.last.name);
    }

    // Calculate total startup time
    _calculateTotalStartupTime();
  }

  /// Mark startup completion
  void markStartupComplete() {
    if (_totalStartupTime != null) return;

    markFirstFrame();
    _calculateTotalStartupTime();
    _analyzeStartupPerformance();

    if (kDebugMode) {
      AnxLog.info('✅ Startup Complete: ${_totalStartupTime!.inMilliseconds}ms');
      _printStartupSummary();
    }
  }

  /// Set startup type (cold/warm)
  void setStartupType(StartupType type) {
    _startupType = type;

    if (kDebugMode) {
      AnxLog.info('🔄 Startup Type: ${type.name}');
    }
  }

  /// Get startup metrics
  StartupMetrics getMetrics() {
    return StartupMetrics(
      totalStartupTime: _totalStartupTime ?? Duration.zero,
      timeToFirstFrame: _timeToFirstFrame ?? Duration.zero,
      startupType: _startupType,
      phases: List.from(_phases),
      bottlenecks: List.from(_bottlenecks),
      performanceScore: _calculatePerformanceScore(),
    );
  }

  /// Calculate total startup time
  void _calculateTotalStartupTime() {
    if (_appStartTime == null || _firstFrameTime == null) return;

    _totalStartupTime = _firstFrameTime!.difference(_appStartTime!);
  }

  /// Check for performance bottlenecks
  void _checkForBottleneck(String phaseName, Duration duration) {
    final thresholdMs = _config.bottleneckThresholds[phaseName] ??
        _config.defaultBottleneckThreshold.inMilliseconds;

    if (duration.inMilliseconds > thresholdMs) {
      final bottleneck = StartupBottleneck(
        phaseName: phaseName,
        duration: duration,
        threshold: Duration(milliseconds: thresholdMs),
        severity: _getBottleneckSeverity(
            duration, Duration(milliseconds: thresholdMs)),
        suggestions: _getOptimizationSuggestions(phaseName),
      );

      _bottlenecks.add(bottleneck);

      if (kDebugMode) {
        AnxLog.warning(
            '🐌 Startup Bottleneck: $phaseName took ${duration.inMilliseconds}ms (threshold: ${thresholdMs}ms)');
      }
    }
  }

  /// Get bottleneck severity
  BottleneckSeverity _getBottleneckSeverity(
      Duration actual, Duration threshold) {
    final multiplier = actual.inMilliseconds / threshold.inMilliseconds;

    if (multiplier >= 3.0) return BottleneckSeverity.severe;
    if (multiplier >= 2.0) return BottleneckSeverity.moderate;
    return BottleneckSeverity.mild;
  }

  /// Get optimization suggestions for a phase
  List<String> _getOptimizationSuggestions(String phaseName) {
    switch (phaseName.toLowerCase()) {
      case 'initialization':
        return [
          'Move non-critical initialization to background',
          'Use lazy loading for heavy services',
          'Optimize SharedPreferences access',
        ];
      case 'database':
        return [
          'Use database connection pooling',
          'Defer non-critical database operations',
          'Optimize database schema and indexes',
        ];
      case 'ui_building':
        return [
          'Use const constructors where possible',
          'Implement widget caching',
          'Reduce initial widget tree complexity',
        ];
      case 'asset_loading':
        return [
          'Preload critical assets only',
          'Use asset bundling optimization',
          'Implement progressive asset loading',
        ];
      default:
        return [
          'Profile the phase for specific bottlenecks',
          'Consider async/await optimization',
          'Check for blocking operations',
        ];
    }
  }

  /// Analyze overall startup performance
  void _analyzeStartupPerformance() {
    if (_totalStartupTime == null) return;

    final totalMs = _totalStartupTime!.inMilliseconds;
    final targetMs = _startupType == StartupType.cold
        ? _config.coldStartTarget.inMilliseconds
        : _config.warmStartTarget.inMilliseconds;

    if (totalMs > targetMs) {
      final severity = totalMs > targetMs * 2
          ? BottleneckSeverity.severe
          : BottleneckSeverity.moderate;

      final bottleneck = StartupBottleneck(
        phaseName: 'overall_startup',
        duration: _totalStartupTime!,
        threshold: Duration(milliseconds: targetMs),
        severity: severity,
        suggestions: [
          'Analyze phase-by-phase timing',
          'Implement progressive loading',
          'Optimize critical path operations',
        ],
      );

      _bottlenecks.add(bottleneck);
    }
  }

  /// Calculate performance score (0-100)
  double _calculatePerformanceScore() {
    if (_totalStartupTime == null) return 0.0;

    final targetMs = _startupType == StartupType.cold
        ? _config.coldStartTarget.inMilliseconds
        : _config.warmStartTarget.inMilliseconds;

    final actualMs = _totalStartupTime!.inMilliseconds;
    final score = (targetMs / actualMs * 100).clamp(0.0, 100.0);

    // Penalize for bottlenecks
    final bottleneckPenalty = _bottlenecks.length * 5.0;

    return (score - bottleneckPenalty).clamp(0.0, 100.0);
  }

  /// Print startup performance summary
  void _printStartupSummary() {
    if (!kDebugMode) return;

    final buffer = StringBuffer();
    buffer.writeln('\n📊 Startup Performance Summary:');
    buffer.writeln('  Type: ${_startupType.name}');
    buffer.writeln('  Total Time: ${_totalStartupTime?.inMilliseconds ?? 0}ms');
    buffer.writeln(
        '  Time to First Frame: ${_timeToFirstFrame?.inMilliseconds ?? 0}ms');
    buffer.writeln(
        '  Performance Score: ${_calculatePerformanceScore().toStringAsFixed(1)}/100');

    if (_phases.isNotEmpty) {
      buffer.writeln('  Phases:');
      for (final phase in _phases) {
        if (phase.isCompleted) {
          buffer.writeln(
              '    ${phase.name}: ${phase.duration!.inMilliseconds}ms');
        }
      }
    }

    if (_bottlenecks.isNotEmpty) {
      buffer.writeln('  Bottlenecks:');
      for (final bottleneck in _bottlenecks) {
        buffer.writeln(
            '    ${bottleneck.phaseName}: ${bottleneck.duration.inMilliseconds}ms (${bottleneck.severity.name})');
      }
    }

    debugPrint(buffer.toString());
  }

  /// Dispose of resources
  void dispose() {
    _phaseStartTimes.clear();
    _phaseDurations.clear();
    _phases.clear();
    _bottlenecks.clear();
    _isInitialized = false;

    if (kDebugMode) {
      AnxLog.info('⏱️ StartupTimeTracker: Disposed');
    }
  }
}

/// Startup time tracking configuration
class StartupConfig {
  const StartupConfig({
    required this.coldStartTarget,
    required this.warmStartTarget,
    required this.defaultBottleneckThreshold,
    required this.bottleneckThresholds,
    required this.enablePhaseLogging,
  });

  final Duration coldStartTarget;
  final Duration warmStartTarget;
  final Duration defaultBottleneckThreshold;
  final Map<String, int> bottleneckThresholds; // phase name -> threshold in ms
  final bool enablePhaseLogging;

  factory StartupConfig.defaultConfig() {
    return StartupConfig(
      coldStartTarget: const Duration(milliseconds: 2000), // 2 seconds
      warmStartTarget: const Duration(milliseconds: 1000), // 1 second
      defaultBottleneckThreshold: const Duration(milliseconds: 500),
      bottleneckThresholds: {
        'initialization': 300,
        'database': 200,
        'ui_building': 400,
        'asset_loading': 300,
        'dictionary': 1000,
        'ai_services': 500,
      },
      enablePhaseLogging: kDebugMode,
    );
  }
}

/// Startup metrics data
class StartupMetrics {
  const StartupMetrics({
    required this.totalStartupTime,
    required this.timeToFirstFrame,
    required this.startupType,
    required this.phases,
    required this.bottlenecks,
    required this.performanceScore,
  });

  final Duration totalStartupTime;
  final Duration timeToFirstFrame;
  final StartupType startupType;
  final List<StartupPhase> phases;
  final List<StartupBottleneck> bottlenecks;
  final double performanceScore;

  /// Check if startup performance is acceptable
  bool get isPerformanceAcceptable => performanceScore >= 70.0;

  /// Get the slowest phase
  StartupPhase? get slowestPhase {
    if (phases.isEmpty) return null;

    return phases.where((p) => p.isCompleted).fold<StartupPhase?>(null,
        (slowest, current) {
      if (slowest == null || current.duration! > slowest.duration!) {
        return current;
      }
      return slowest;
    });
  }
}

/// Startup phase data
class StartupPhase {
  const StartupPhase({
    required this.name,
    required this.startTime,
    required this.order,
    this.endTime,
    this.duration,
  });

  final String name;
  final DateTime startTime;
  final int order;
  final DateTime? endTime;
  final Duration? duration;

  bool get isCompleted => endTime != null && duration != null;

  StartupPhase copyWith({
    String? name,
    DateTime? startTime,
    int? order,
    DateTime? endTime,
    Duration? duration,
  }) {
    return StartupPhase(
      name: name ?? this.name,
      startTime: startTime ?? this.startTime,
      order: order ?? this.order,
      endTime: endTime ?? this.endTime,
      duration: duration ?? this.duration,
    );
  }
}

/// Startup bottleneck data
class StartupBottleneck {
  const StartupBottleneck({
    required this.phaseName,
    required this.duration,
    required this.threshold,
    required this.severity,
    required this.suggestions,
  });

  final String phaseName;
  final Duration duration;
  final Duration threshold;
  final BottleneckSeverity severity;
  final List<String> suggestions;

  /// Get performance impact multiplier
  double get impactMultiplier =>
      duration.inMilliseconds / threshold.inMilliseconds;
}

/// Startup type enumeration
enum StartupType {
  cold,
  warm,
}

/// Bottleneck severity levels
enum BottleneckSeverity {
  mild,
  moderate,
  severe,
}
