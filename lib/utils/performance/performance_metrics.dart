import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'frame_rate_monitor.dart';
import 'startup_time_tracker.dart';
import 'memory_usage_monitor.dart';
import 'cpu_usage_monitor.dart';
import 'network_performance_monitor.dart';
import 'battery_consumption_monitor.dart';
import 'performance_optimizer.dart';

/// Comprehensive performance metrics system for Dasso Reader
///
/// This system provides:
/// - Frame rate monitoring and jank detection
/// - App startup time measurement
/// - Memory usage tracking and optimization
/// - CPU usage monitoring and profiling
/// - Network performance optimization
/// - Battery consumption monitoring
/// - DevTools integration for performance profiling
class PerformanceMetrics {
  static final PerformanceMetrics _instance = PerformanceMetrics._internal();
  factory PerformanceMetrics() => _instance;
  PerformanceMetrics._internal();

  // Performance monitoring state
  bool _isInitialized = false;
  bool _isMonitoring = false;
  Timer? _monitoringTimer;

  // Frame rate monitoring
  final FrameRateMonitor _frameRateMonitor = FrameRateMonitor();

  // Startup time tracking
  final StartupTimeTracker _startupTracker = StartupTimeTracker();

  // Memory usage monitoring
  final MemoryUsageMonitor _memoryMonitor = MemoryUsageMonitor();

  // CPU usage monitoring
  final CPUUsageMonitor _cpuMonitor = CPUUsageMonitor();

  // Network performance monitoring
  final NetworkPerformanceMonitor _networkMonitor = NetworkPerformanceMonitor();

  // Battery consumption monitoring
  final BatteryConsumptionMonitor _batteryMonitor = BatteryConsumptionMonitor();

  // Performance optimization system
  final PerformanceOptimizer _performanceOptimizer = PerformanceOptimizer();

  /// Initialize the performance metrics system
  Future<void> initialize({
    PerformanceConfig? config,
  }) async {
    if (_isInitialized) return;

    // Initialize in all modes, but with different configurations
    // Release mode gets essential monitoring only (battery, memory optimization)

    final effectiveConfig = config ?? PerformanceConfig.defaultConfig();

    try {
      if (kReleaseMode) {
        // Release mode: Initialize essential components only
        await _memoryMonitor.initialize(effectiveConfig.memoryConfig);
        await _batteryMonitor.initialize(effectiveConfig.batteryConfig);
        await _performanceOptimizer.initialize();
      } else {
        // Debug/Profile mode: Initialize all monitoring components
        await _frameRateMonitor.initialize(effectiveConfig.frameRateConfig);
        await _startupTracker.initialize(effectiveConfig.startupConfig);
        await _memoryMonitor.initialize(effectiveConfig.memoryConfig);
        await _cpuMonitor.initialize(effectiveConfig.cpuConfig);
        await _networkMonitor.initialize(effectiveConfig.networkConfig);
        await _batteryMonitor.initialize(effectiveConfig.batteryConfig);
        await _performanceOptimizer.initialize();
      }

      _isInitialized = true;

      // Log only in debug and profile modes
      if (!kReleaseMode) {
        AnxLog.info('🚀 PerformanceMetrics: Initialized successfully');
      }
    } catch (error) {
      if (!kReleaseMode) {
        AnxLog.severe('❌ PerformanceMetrics: Initialization failed: $error');
      }
      rethrow;
    }
  }

  /// Start performance monitoring
  void startMonitoring() {
    if (!_isInitialized || _isMonitoring) return;

    _isMonitoring = true;

    if (kReleaseMode) {
      // Release mode: Start essential monitoring only
      _memoryMonitor.startMonitoring();
      _batteryMonitor.startMonitoring();
    } else {
      // Debug/Profile mode: Start all monitoring components
      _frameRateMonitor.startMonitoring();
      _memoryMonitor.startMonitoring();
      _cpuMonitor.startMonitoring();
      _networkMonitor.startMonitoring();
      _batteryMonitor.startMonitoring();

      // Set up periodic performance reporting (debug/profile only)
      _setupPeriodicReporting();

      // Log in both debug and profile modes
      AnxLog.info('📊 PerformanceMetrics: Started monitoring');
    }
  }

  /// Stop performance monitoring
  void stopMonitoring() {
    if (!_isMonitoring) return;

    _isMonitoring = false;
    _monitoringTimer?.cancel();
    _monitoringTimer = null;

    // Stop all monitoring components
    _frameRateMonitor.stopMonitoring();
    _memoryMonitor.stopMonitoring();
    _cpuMonitor.stopMonitoring();
    _networkMonitor.stopMonitoring();
    _batteryMonitor.stopMonitoring();

    if (kDebugMode) {
      AnxLog.info('📊 PerformanceMetrics: Stopped monitoring');
    }
  }

  /// Get comprehensive performance report
  PerformanceReport getPerformanceReport() {
    if (kReleaseMode) {
      // Release mode: Return limited metrics with defaults for unavailable data
      return PerformanceReport(
        frameRateMetrics: const FrameRateMetrics(
          currentFPS: 60.0,
          averageFPS: 60.0,
          targetFPS: 60.0,
          jankCount: 0,
          totalFrames: 0,
          jankEvents: [],
          performanceScore: 100.0,
        ),
        startupMetrics: const StartupMetrics(
          totalStartupTime: Duration.zero,
          timeToFirstFrame: Duration.zero,
          startupType: StartupType.cold,
          phases: [],
          bottlenecks: [],
          performanceScore: 100.0,
        ),
        memoryMetrics: _memoryMonitor.getMetrics(),
        cpuMetrics: const CPUMetrics(
          currentUsage: 0.0,
          averageUsage: 0.0,
          peakUsage: 0.0,
          cpuHistory: [],
          hotspots: [],
          backgroundTaskCount: 0,
          isIdleState: true,
          performanceScore: 100.0,
        ),
        networkMetrics: const NetworkMetrics(
          averageResponseTime: Duration.zero,
          totalRequests: 0,
          failedRequests: 0,
          successRate: 100.0,
          performanceScore: 100.0,
        ),
        batteryMetrics: _batteryMonitor.getMetrics(),
        timestamp: DateTime.now(),
      );
    } else {
      // Debug/Profile mode: Return full metrics
      return PerformanceReport(
        frameRateMetrics: _frameRateMonitor.getMetrics(),
        startupMetrics: _startupTracker.getMetrics(),
        memoryMetrics: _memoryMonitor.getMetrics(),
        cpuMetrics: _cpuMonitor.getMetrics(),
        networkMetrics: _networkMonitor.getMetrics(),
        batteryMetrics: _batteryMonitor.getMetrics(),
        timestamp: DateTime.now(),
      );
    }
  }

  /// Track app startup phase
  void trackStartupPhase(String phase) {
    if (kReleaseMode) return;
    _startupTracker.trackPhase(phase);
  }

  /// Track network request performance
  void trackNetworkRequest(NetworkRequestMetrics metrics) {
    if (kReleaseMode) return;
    _networkMonitor.trackRequest(metrics);
  }

  /// Track custom performance event
  void trackCustomEvent(String eventName, Map<String, dynamic> data) {
    if (kReleaseMode) return;
    if (kDebugMode) {
      AnxLog.info('📊 Custom Performance Event: $eventName - $data');
    }
  }

  /// Set up periodic performance reporting
  void _setupPeriodicReporting() {
    // Enable in both debug and profile modes
    if (kReleaseMode) return;

    _monitoringTimer = Timer.periodic(
      const Duration(minutes: 5),
      (_) => _printPerformanceReport(),
    );
  }

  /// Print comprehensive performance report
  void _printPerformanceReport() {
    // Enable in both debug and profile modes
    if (kReleaseMode) return;

    final report = getPerformanceReport();
    const separator =
        '============================================================';

    AnxLog.info('\n$separator');
    AnxLog.info('📊 DASSO READER PERFORMANCE METRICS REPORT');
    AnxLog.info(separator);
    AnxLog.info(report.toString());
    AnxLog.info('$separator\n');
  }

  /// Dispose of all resources
  void dispose() {
    stopMonitoring();

    _frameRateMonitor.dispose();
    _startupTracker.dispose();
    _memoryMonitor.dispose();
    _cpuMonitor.dispose();
    _networkMonitor.dispose();
    _batteryMonitor.dispose();
    _performanceOptimizer.dispose();

    _isInitialized = false;

    if (kDebugMode) {
      AnxLog.info('🚀 PerformanceMetrics: Disposed');
    }
  }

  // Getters for individual monitoring components
  FrameRateMonitor get frameRateMonitor => _frameRateMonitor;
  StartupTimeTracker get startupTracker => _startupTracker;
  MemoryUsageMonitor get memoryMonitor => _memoryMonitor;
  CPUUsageMonitor get cpuMonitor => _cpuMonitor;
  NetworkPerformanceMonitor get networkMonitor => _networkMonitor;
  BatteryConsumptionMonitor get batteryMonitor => _batteryMonitor;

  // Convenience getters for quick access to metrics
  bool get isPerformingWell => getPerformanceReport().overallScore >= 80;
  double get currentFPS => _frameRateMonitor.currentFPS;
  int get memoryUsageMB => _memoryMonitor.currentMemoryUsageMB;
  double get cpuUsagePercent => _cpuMonitor.currentCPUUsage;
  int get batteryLevel => _batteryMonitor.currentBatteryLevel;

  // Status getters for diagnostics
  bool get isInitialized => _isInitialized;
  bool get isMonitoring => _isMonitoring;
}

/// Performance configuration for the metrics system
class PerformanceConfig {
  const PerformanceConfig({
    required this.frameRateConfig,
    required this.startupConfig,
    required this.memoryConfig,
    required this.cpuConfig,
    required this.networkConfig,
    required this.batteryConfig,
  });

  final FrameRateConfig frameRateConfig;
  final StartupConfig startupConfig;
  final MemoryConfig memoryConfig;
  final CPUConfig cpuConfig;
  final NetworkConfig networkConfig;
  final BatteryConfig batteryConfig;

  factory PerformanceConfig.defaultConfig() {
    return PerformanceConfig(
      frameRateConfig: FrameRateConfig.defaultConfig(),
      startupConfig: StartupConfig.defaultConfig(),
      memoryConfig: MemoryConfig.defaultConfig(),
      cpuConfig: CPUConfig.defaultConfig(),
      networkConfig: NetworkConfig.defaultConfig(),
      batteryConfig: BatteryConfig.defaultConfig(),
    );
  }
}

/// Comprehensive performance report
class PerformanceReport {
  const PerformanceReport({
    required this.frameRateMetrics,
    required this.startupMetrics,
    required this.memoryMetrics,
    required this.cpuMetrics,
    required this.networkMetrics,
    required this.batteryMetrics,
    required this.timestamp,
  });

  final FrameRateMetrics frameRateMetrics;
  final StartupMetrics startupMetrics;
  final MemoryMetrics memoryMetrics;
  final CPUMetrics cpuMetrics;
  final NetworkMetrics networkMetrics;
  final BatteryMetrics batteryMetrics;
  final DateTime timestamp;

  /// Calculate overall performance score (0-100)
  double get overallScore {
    final scores = [
      frameRateMetrics.performanceScore,
      startupMetrics.performanceScore,
      memoryMetrics.performanceScore,
      cpuMetrics.performanceScore,
      networkMetrics.performanceScore,
      batteryMetrics.performanceScore,
    ];

    return scores.reduce((a, b) => a + b) / scores.length;
  }

  /// Get performance grade (A-F)
  String get performanceGrade {
    final score = overallScore;
    if (score >= 90) return 'A';
    if (score >= 80) return 'B';
    if (score >= 70) return 'C';
    if (score >= 60) return 'D';
    return 'F';
  }

  @override
  String toString() {
    final buffer = StringBuffer();

    buffer.writeln(
        'Overall Performance Score: ${overallScore.toStringAsFixed(1)}/100 (Grade: $performanceGrade)');
    buffer.writeln('');
    buffer.writeln(
        'Frame Rate: ${frameRateMetrics.averageFPS.toStringAsFixed(1)} FPS (${frameRateMetrics.jankCount} jank events)');
    buffer.writeln(
        'Startup Time: ${startupMetrics.totalStartupTime.inMilliseconds}ms');
    buffer.writeln(
        'Memory Usage: ${memoryMetrics.currentUsageMB}MB (Peak: ${memoryMetrics.peakUsageMB}MB)');
    buffer.writeln('CPU Usage: ${cpuMetrics.averageUsage.toStringAsFixed(1)}%');
    buffer.writeln(
        'Network: ${networkMetrics.averageResponseTime.inMilliseconds}ms avg response');
    buffer.writeln('Battery Level: ${batteryMetrics.currentLevel}%');

    return buffer.toString();
  }
}
