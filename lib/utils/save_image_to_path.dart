import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:dasso_reader/utils/log/common.dart';

Future<String> saveB64ImageToPath(
    String image, String path, String? name) async {
  try {
    // image is base64 encoded
    // data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD//gA8Q1JFQVRPUjogZ2...
    final List<String> parts = image.split(',');

    // Validate that we have the expected format
    if (parts.length < 2) {
      AnxLog.severe(
          'Error saving image: Invalid base64 format, expected "data:type;base64,data" but got: ${image.length > 100 ? image.substring(0, 100) + '...' : image}');
      return '';
    }

    final String base64String = parts[1];
    final Uint8List pngBytes = base64.decode(base64String);

    // Safely extract extension
    final headerParts = parts[0].split('/');
    if (headerParts.length < 2) {
      AnxLog.severe('Error saving image: Invalid header format: ${parts[0]}');
      return '';
    }

    final extensionParts = headerParts[1].split(';');
    if (extensionParts.isEmpty) {
      AnxLog.severe(
          'Error saving image: Invalid extension format: ${headerParts[1]}');
      return '';
    }

    final extension = extensionParts[0];

    name = '$name.$extension';
    path = '$path/$name';

    final file = File(path);
    await file.writeAsBytes(pngBytes);

    return path;
  } catch (e) {
    AnxLog.severe('Error saving image: $e');
    return '';
  }
}
