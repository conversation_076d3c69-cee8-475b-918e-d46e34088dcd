import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/providers/anx_webdav.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../enums/sync_direction.dart';

void showWebdavStatus(BuildContext context) {
  showDialog(
    context: context,
    builder: (context) {
      return const SyncStatusDialog();
    },
  );
}

class SyncStatusDialog extends ConsumerStatefulWidget {
  const SyncStatusDialog({super.key});

  @override
  SyncStatusDialogState createState() => SyncStatusDialogState();
}

class SyncStatusDialogState extends ConsumerState<SyncStatusDialog> {
  @override
  Widget build(BuildContext context) {
    final syncState = ref.watch(anxWebdavProvider);

    String dir = syncState.direction == SyncDirection.upload
        ? L10n.of(context).common_uploading
        : L10n.of(context).common_downloading;
    return AlertDialog(
      title: Text(dir),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(bottom: 10),
                child: Text(
                  syncState.fileName,
                ),
              ),
            ],
          ),
          LinearProgressIndicator(
            value: syncState.count / syncState.total,
          ),
          Text(
              '${byteToHuman(syncState.count)} / ${byteToHuman(syncState.total)}'),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: Text(L10n.of(context).common_ok),
        ),
      ],
    );
  }

  String byteToHuman(int byte) {
    if (byte < 1024) {
      return '$byte B';
    } else if (byte < 1024 * 1024) {
      return '${(byte / 1024).toStringAsFixed(2)} KB';
    } else if (byte < 1024 * 1024 * 1024) {
      return '${(byte / 1024 / 1024).toStringAsFixed(2)} MB';
    } else {
      return '${(byte / 1024 / 1024 / 1024).toStringAsFixed(2)} GB';
    }
  }
}
