import 'package:flutter/material.dart';
import 'package:flutter/semantics.dart';

/// Screen reader support and optimization system
///
/// This system provides comprehensive screen reader support including:
/// - Optimized content ordering and navigation
/// - Context-aware announcements
/// - Reading flow optimization
/// - Screen reader testing utilities
class ScreenReaderSupport {
  // Private constructor to prevent instantiation
  ScreenReaderSupport._();

  // =====================================================
  // SCREEN READER DETECTION
  // =====================================================

  /// Check if screen reader is active
  static bool isScreenReaderActive(BuildContext context) {
    return MediaQuery.of(context).accessibleNavigation;
  }

  /// Check if high contrast is enabled
  static bool isHighContrastEnabled(BuildContext context) {
    return MediaQuery.of(context).highContrast;
  }

  /// Check if bold text is enabled
  static bool isBoldTextEnabled(BuildContext context) {
    return MediaQuery.of(context).boldText;
  }

  // =====================================================
  // CONTENT OPTIMIZATION
  // =====================================================

  /// Optimize content for screen readers
  static Widget optimizeForScreenReader({
    required BuildContext context,
    required Widget child,
    String? pageTitle,
    String? pageDescription,
  }) {
    if (!isScreenReaderActive(context)) {
      return child;
    }

    return Semantics(
      label: pageTitle,
      hint: pageDescription,
      child: child,
    );
  }

  /// Create screen reader optimized list
  static Widget optimizedList({
    required BuildContext context,
    required List<Widget> children,
    required String listTitle,
    String? contentType,
  }) {
    if (!isScreenReaderActive(context)) {
      return Column(children: children);
    }

    String label = '$listTitle with ${children.length} items';
    if (contentType != null) {
      label = '$contentType $label';
    }

    return Semantics(
      label: label,
      child: Column(
        children: children.asMap().entries.map((entry) {
          final index = entry.key;
          final child = entry.value;

          return Semantics(
            label: 'Item ${index + 1} of ${children.length}',
            child: child,
          );
        }).toList(),
      ),
    );
  }

  /// Create screen reader optimized navigation
  static Widget optimizedNavigation({
    required BuildContext context,
    required List<Widget> navigationItems,
    required String navigationLabel,
    int? selectedIndex,
  }) {
    if (!isScreenReaderActive(context)) {
      return Row(children: navigationItems);
    }

    return Semantics(
      label: '$navigationLabel with ${navigationItems.length} options',
      child: Row(
        children: navigationItems.asMap().entries.map((entry) {
          final index = entry.key;
          final child = entry.value;
          final isSelected = selectedIndex == index;

          return Semantics(
            label: 'Navigation item ${index + 1} of ${navigationItems.length}',
            selected: isSelected,
            child: child,
          );
        }).toList(),
      ),
    );
  }

  // =====================================================
  // READING FLOW OPTIMIZATION
  // =====================================================

  /// Create logical reading order container
  static Widget createReadingOrderContainer({
    required List<Widget> children,
    String? containerLabel,
  }) {
    return Semantics(
      label: containerLabel,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      ),
    );
  }

  /// Group related content for screen readers
  static Widget groupRelatedContent({
    required List<Widget> children,
    required String groupLabel,
    String? groupHint,
  }) {
    return MergeSemantics(
      child: Semantics(
        label: groupLabel,
        hint: groupHint,
        child: Column(children: children),
      ),
    );
  }

  /// Create content hierarchy for screen readers
  static Widget createContentHierarchy({
    Widget? header,
    Widget? subheader,
    required Widget content,
    Widget? footer,
    String? sectionLabel,
  }) {
    final children = <Widget>[];

    if (header != null) {
      children.add(Semantics(
        header: true,
        child: header,
      ));
    }

    if (subheader != null) {
      children.add(Semantics(
        header: true,
        child: subheader,
      ));
    }

    children.add(content);

    if (footer != null) {
      children.add(footer);
    }

    return Semantics(
      label: sectionLabel,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      ),
    );
  }

  // =====================================================
  // ANNOUNCEMENTS
  // =====================================================

  /// Announce navigation change
  static void announceNavigation({
    required String destination,
    String? additionalContext,
  }) {
    String announcement = 'Navigated to $destination';
    if (additionalContext != null) {
      announcement = '$announcement. $additionalContext';
    }

    SemanticsService.announce(
      announcement,
      TextDirection.ltr,
    );
  }

  /// Announce content change
  static void announceContentChange({
    required String changeDescription,
    bool isImportant = false,
  }) {
    String announcement = changeDescription;
    if (isImportant) {
      announcement = 'Important: $announcement';
    }

    SemanticsService.announce(
      announcement,
      TextDirection.ltr,
    );
  }

  /// Announce loading state
  static void announceLoading({
    required String loadingDescription,
  }) {
    SemanticsService.announce(
      'Loading $loadingDescription',
      TextDirection.ltr,
    );
  }

  /// Announce completion
  static void announceCompletion({
    required String completionDescription,
  }) {
    SemanticsService.announce(
      'Completed $completionDescription',
      TextDirection.ltr,
    );
  }

  /// Announce error
  static void announceError({
    required String errorDescription,
  }) {
    SemanticsService.announce(
      'Error: $errorDescription',
      TextDirection.ltr,
    );
  }

  // =====================================================
  // CONTENT-SPECIFIC OPTIMIZATIONS
  // =====================================================

  /// Optimize book content for screen readers
  static Widget optimizeBookContent({
    required BuildContext context,
    required Widget bookContent,
    required String bookTitle,
    required String author,
    double? progress,
  }) {
    String label = 'Reading $bookTitle by $author';
    if (progress != null) {
      label = '$label. Progress: ${(progress * 100).toStringAsFixed(0)}%';
    }

    return Semantics(
      label: label,
      child: bookContent,
    );
  }

  /// Optimize dictionary content for screen readers
  static Widget optimizeDictionaryContent({
    required BuildContext context,
    required Widget dictionaryContent,
    required String word,
    String? pinyin,
    String? definition,
    int? hskLevel,
  }) {
    String label = 'Dictionary entry for $word';

    if (pinyin != null) {
      label = '$label, pronounced $pinyin';
    }

    if (definition != null) {
      label = '$label, meaning: $definition';
    }

    if (hskLevel != null) {
      label = '$label, HSK Level $hskLevel';
    }

    return Semantics(
      label: label,
      child: dictionaryContent,
    );
  }

  /// Optimize HSK content for screen readers
  static Widget optimizeHskContent({
    required BuildContext context,
    required Widget hskContent,
    required int level,
    String? currentWord,
    int? progress,
    int? total,
  }) {
    String label = 'HSK Level $level practice';

    if (currentWord != null) {
      label = '$label, current word: $currentWord';
    }

    if (progress != null && total != null) {
      label = '$label, question $progress of $total';
    }

    return Semantics(
      label: label,
      child: hskContent,
    );
  }

  // =====================================================
  // TESTING UTILITIES
  // =====================================================

  /// Test widget accessibility for screen readers
  static ScreenReaderTestResult testWidgetAccessibility({
    required Widget widget,
    required BuildContext context,
  }) {
    final issues = <String>[];
    final suggestions = <String>[];

    // Check for semantic labels
    // This would be implemented with widget inspection

    // Check for logical reading order
    // This would be implemented with semantic tree analysis

    // Check for proper grouping
    // This would be implemented with semantic structure analysis

    return ScreenReaderTestResult(
      isAccessible: issues.isEmpty,
      issues: issues,
      suggestions: suggestions,
    );
  }

  /// Generate accessibility report
  static AccessibilityReport generateAccessibilityReport({
    required BuildContext context,
    required List<Widget> widgets,
  }) {
    final results = widgets
        .map((widget) =>
            testWidgetAccessibility(widget: widget, context: context))
        .toList();

    final totalIssues =
        results.fold<int>(0, (sum, result) => sum + result.issues.length);
    final totalSuggestions =
        results.fold<int>(0, (sum, result) => sum + result.suggestions.length);

    return AccessibilityReport(
      totalWidgets: widgets.length,
      accessibleWidgets: results.where((r) => r.isAccessible).length,
      totalIssues: totalIssues,
      totalSuggestions: totalSuggestions,
      results: results,
    );
  }
}

/// Result of screen reader accessibility testing
class ScreenReaderTestResult {
  const ScreenReaderTestResult({
    required this.isAccessible,
    required this.issues,
    required this.suggestions,
  });

  final bool isAccessible;
  final List<String> issues;
  final List<String> suggestions;
}

/// Comprehensive accessibility report
class AccessibilityReport {
  const AccessibilityReport({
    required this.totalWidgets,
    required this.accessibleWidgets,
    required this.totalIssues,
    required this.totalSuggestions,
    required this.results,
  });

  final int totalWidgets;
  final int accessibleWidgets;
  final int totalIssues;
  final int totalSuggestions;
  final List<ScreenReaderTestResult> results;

  /// Calculate accessibility score (0-100)
  double get accessibilityScore {
    if (totalWidgets == 0) return 100.0;
    return (accessibleWidgets / totalWidgets) * 100.0;
  }

  /// Check if accessibility is good (>= 90%)
  bool get isAccessibilityGood => accessibilityScore >= 90.0;
}
