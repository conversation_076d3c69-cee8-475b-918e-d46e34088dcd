enum AiPrompts {
  test,
  summaryTheChapter,
  summaryTheBook,
  summaryThePreviousContent,
  grammar,
}

extension AiPrompts<PERSON>son on AiPrompts {
  String getPrompt() {
    switch (this) {
      case AiPrompts.test:
        return '''
        Write a concise and friendly self-introduction. Use the language code: {{language_locale}}
        ''';

      case AiPrompts.summaryTheChapter:
        return '''
Summarize the chapter content. Your reply must follow these requirements:
Language: Use the same language as the original chapter content.
Length: 8-10 complete sentences.
Structure: Three paragraphs: Main plot, Core characters, Themes/messages.
Style: Avoid boilerplate phrases like "This chapter describes..."
Perspective: Maintain a literary analysis perspective, not just narration.
Chapter content: {{chapter}}
        ''';

      case AiPrompts.summaryTheBook:
        return '''
Generate a book summary for "{{book}}" by {{author}}
[Requirements]:
Language matches the book title's language
Central conflict (highlight with » symbol)
3 core characters + their motivations (name + critical choice)
Theme keywords (3-5)
Avoid spoiling the final outcome
        ''';

      case AiPrompts.summaryThePreviousContent:
        return '''
I'm revisiting a book I read long ago. Help me quickly recall the previous content to continue reading:
[Requirements]
3-5 sentences
Same language as original previous content
Avoid verbatim repetition; preserve core information

[Previous Content]
{{previous_content}}
        ''';

      case AiPrompts.grammar:
        return '''
Grammar Point: [Identify the key grammar rule in the text, e.g., "了 (le) for completed actions"].

Explanation (English): [1-2 sentences explaining the rule simply].

Explanation (Arabic): [شرح عربي مختصر].

Examples:

Chinese: {{text}}
Pinyin: [Pinyin with tone marks]
English: [Translation]
Arabic: [ترجمة عربية]

Chinese: [Create a new example sentence]
Pinyin: [Pinyin]
English/Arabic: [Translations]

Pronunciation Guide: Highlight key words with pinyin and tones (e.g., 了 → le).

Key Notes: [List 2-3 common mistakes or tips].
        ''';
    }
  }
}
