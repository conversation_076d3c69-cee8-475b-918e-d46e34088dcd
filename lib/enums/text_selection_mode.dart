/// Enum for text selection modes in the ebook reader
enum TextSelectionMode {
  /// Free selection mode - users can freely select any text portion
  free,
  
  /// Segmentation selection mode - uses Chinese word segmentation for structured text selection
  segmentation;

  /// Get the display name for the selection mode
  String get displayName {
    switch (this) {
      case TextSelectionMode.free:
        return 'Free Selection';
      case TextSelectionMode.segmentation:
        return 'Word Segmentation';
    }
  }

  /// Get the mode from string name
  static TextSelectionMode fromString(String name) {
    return TextSelectionMode.values.firstWhere(
      (mode) => mode.name == name,
      orElse: () => TextSelectionMode.free, // Default to free selection
    );
  }
}
