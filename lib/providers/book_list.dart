import 'package:dasso_reader/dao/book.dart' as book_dao;
import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/utils/state_management/provider_optimization.dart';
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'book_list.g.dart';

@riverpod
class BookList extends _$BookList {
  List<List<Book>> groupBooks(List<Book> books) {
    var groupedBooks = <List<Book>>[];
    for (var book in books) {
      if (book.groupId == 0) {
        groupedBooks.add([book]);
      } else {
        var existingGroup = groupedBooks.firstWhere(
          (group) => group.first.groupId == book.groupId,
          orElse: () => [],
        );
        if (existingGroup.isEmpty) {
          groupedBooks.add([book]);
        } else {
          existingGroup.add(book);
        }
      }
    }
    return groupedBooks;
  }

  @override
  Future<List<List<Book>>> build() async {
    if (kDebugMode) {
      ProviderPerformanceMonitor.recordAccess('BookList.build');
    }

    final books = await book_dao.selectNotDeleteBooks();
    final result = groupBooks(books);

    if (kDebugMode) {
      ProviderPerformanceMonitor.recordRebuild('BookList.build');
      debugPrint(
          '📚 BookList: Loaded ${books.length} books in ${result.length} groups');
    }

    return result;
  }

  Future<void> refresh() async {
    if (kDebugMode) {
      ProviderPerformanceMonitor.recordAccess('BookList.refresh');
      debugPrint('🔄 BookList: Refreshing book list');
    }

    state = const AsyncValue.loading();
    try {
      final newData = await build();
      state = AsyncValue.data(newData);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  void moveBook(Book data, int groupId) {
    if (kDebugMode) {
      debugPrint('📦 BookList: Moving book ${data.title} to group $groupId');
    }
    updateBook(data.copyWith(groupId: groupId));
    refresh();
  }

  void updateBook(Book book) {
    if (kDebugMode) {
      debugPrint('📝 BookList: Updating book ${book.title}');
    }
    book_dao.updateBook(book);
    refresh();
  }

  void dissolveGroup(List<Book> books) {
    for (var book in books) {
      updateBook(book.copyWith(groupId: 0));
    }
    refresh();
  }

  void removeFromGroup(Book book) {
    updateBook(book.copyWith(groupId: 0));
    refresh();
  }

  void reorder(List<List<Book>> books) {
    state = AsyncData(books);
  }

  void moveBookToTop(int bookId) {
    var groups = state.value!.map((group) {
      if (group.any((book) => book.id == bookId)) {
        return [
          group.firstWhere((book) => book.id == bookId),
          ...group.where((b) => b.id != bookId)
        ];
      }
      return group;
    }).toList();

    state = AsyncData([
      groups.firstWhere((group) => group.any((book) => book.id == bookId)),
      ...groups.where((group) => group.every((book) => book.id != bookId))
    ]);
  }

  Future<void> search(String? value) async {
    if (value == null || value.isEmpty) {
      state = AsyncData(await build());
      return;
    }

    final books = await book_dao.selectNotDeleteBooks();

    final lowerCaseValue = value.toLowerCase();

    final filteredBooks = books.where((book) {
      return book.title.toLowerCase().contains(lowerCaseValue) ||
          book.author.toLowerCase().contains(lowerCaseValue);
    }).toList();

    final groupedBooks = groupBooks(filteredBooks);
    state = AsyncData(groupedBooks);
  }
}

/// Optimized provider selectors for BookList to minimize rebuilds
///
/// These providers only rebuild when specific aspects of the book list change,
/// reducing unnecessary widget rebuilds throughout the app.

/// Provider that only rebuilds when the total book count changes
@riverpod
int bookCount(ref) {
  final bookListAsync = ref.watch(bookListProvider);
  return bookListAsync.when(
    data: (books) {
      final count = books.fold<int>(0, (sum, group) => sum + group.length);
      if (kDebugMode) {
        debugPrint('📊 BookCount: $count books total');
      }
      return count;
    },
    loading: () => 0,
    error: (_, __) => 0,
  );
}

/// Provider that only rebuilds when the number of groups changes
@riverpod
int bookGroupCount(ref) {
  final bookListAsync = ref.watch(bookListProvider);
  return bookListAsync.when(
    data: (books) {
      if (kDebugMode) {
        debugPrint('📊 BookGroupCount: ${books.length} groups');
      }
      return books.length;
    },
    loading: () => 0,
    error: (_, __) => 0,
  );
}

/// Provider that only rebuilds when books with reading progress change
@riverpod
int booksInProgressCount(ref) {
  final bookListAsync = ref.watch(bookListProvider);
  return bookListAsync.when(
    data: (books) {
      final count = books
          .expand((group) => group)
          .where((book) =>
              book.readingPercentage > 0 && book.readingPercentage < 1)
          .length;
      if (kDebugMode) {
        debugPrint('📊 BooksInProgress: $count books');
      }
      return count;
    },
    loading: () => 0,
    error: (_, __) => 0,
  );
}
