{"appName": "Anx <PERSON>", "app_about": "About Anx <PERSON>", "app_version": "Version", "app_license": "License", "app_author": "Author", "app_donate": "Donate", "app_donate_tips": "Anx Reader is an open-source software that could free to use. If you find it helpful, please donate to support us to continue improving it.", "navBar_bookshelf": "Bookshelf", "navBar_dictionary": "Dictionary", "navBar_vocabulary": "Vocabulary", "navBar_hsk": "HSK", "navBar_statistics": "Stats", "navBar_notes": "Notes", "navBar_settings": "Settings", "settings_dark_mode": "Dark", "settings_light_mode": "Light", "settings_system_mode": "System", "settings_moreSettings": "More Settings", "settings_appearance": "Appearance", "settings_appearance_theme": "Theme", "settings_appearance_themeColor": "Theme Color", "settings_appearance_display": "Display", "settings_appearance_language": "Language", "settings_appearance_open_book_animation": "Open Book Animation", "settings_appearance_bottom_navigator_show": "Bottom Navigator", "e_ink_mode": "E-ink Mode", "reading_contents": "Contents", "statistic_to_present": "To Present", "statistic_books_read": "{ count, plural, =0{No books read} =1{1 book read} other{{count} books read} }", "statistic_days_of_reading": "{ count, plural, =0{No days read} =1{1 day read} other{{count} days read} }", "statistic_notes": "{ count, plural, =0{No notes} =1{1 note} other{{count} notes} }", "statistic_week": "week", "statistic_month": "month", "statistic_year": "year", "statistic_all": "all", "statistic_all_time": "All Time", "statistic_this_week": "This Week", "statistic_monday": "Mn", "statistic_tuesday": "Tu", "statistic_wednesday": "Wd", "statistic_thursday": "Th", "statistic_friday": "Fr", "statistic_saturday": "Sa", "statistic_sunday": "Su", "notes_notes_across": "{ count, plural, =0{No notes} =1{1 note} other{{count} notes} } across", "notes_books": "{ count, plural, =0{No books} =1{1 book} other{{count} books} }", "notes_notes": "{ count, plural, =0{No notes} =1{1 note} other{{count} notes} }", "notes_read_percentage": "{percentage} read", "reading_page_copy": "Copy", "reading_page_excerpt": "Excerpt", "reading_page_theme": "Theme", "reading_page_style": "Style", "reading_page_other": "Other", "reading_page_reading": "Reading", "reading_page_convert_chinese": "Convert Chinese", "reading_page_original": "Original", "reading_page_simplified": "Simplified", "reading_page_traditional": "Traditional", "reading_page_convert_chinese_tips": "Convert Chinese may not be accurate, please use it carefully", "reading_page_column_count": "Column Count", "reading_page_auto": "Auto", "reading_page_single": "Single", "reading_page_double": "Double", "reading_page_bionic_reading": "Bionic Reading", "reading_page_bionic_reading_tips": "What is Bionic Reading?", "reading_page_header_settings": "Head<PERSON>", "reading_page_footer_settings": "<PERSON><PERSON>s", "reading_page_left": "Left", "reading_page_center": "Center", "reading_page_right": "Right", "context_menu_add_note_tips": "Add your note", "context_menu_copy": "Copy", "context_menu_search": "Search", "context_menu_translate": "Translate", "context_menu_write_idea": "Write Idea", "context_menu_delete": "Delete", "context_menu_confirm": "Confirm", "context_menu_highlight": "Highlight", "context_menu_underline": "Underline", "reading_page_full_screen": "Full Screen", "reading_page_screen_timeout": "Screen Timeout", "reading_page_page_turning_method": "Page Turning", "font": "Font", "add_new_font": "Add New Font", "follow_book": "Follow Book", "system_font": "System Font", "page_turning_style": "Page Turning Style", "no_animation": "No Animation", "slide": "Slide", "scroll": "<PERSON><PERSON>", "book_deleted": "Book deleted", "import_n_books_selected": "{count, plural, =1{1 book selected} other{{count} books selected}}", "import_support_types": "Support types: {types}", "import_n_books_not_support": "{count, plural, =1{1 book not support yet} other{{count} books not support yet}}", "import_import_n_books": "{count, plural, =1{Import 1 book} other{Import {count} books}}", "bookshelf_tips_1": "There are no books.", "bookshelf_tips_2": "Click the add button to add a book!", "statistics_tips_1": "No reading in the selected time period.", "statistics_tips_2": "A book is a dream that you hold in your hands.", "notes_tips_1": "There are no notes.", "notes_tips_2": "Add a note while reading!", "reading_page_chapter_pages": "Chapter Pages", "reading_page_current_page": "Current Page", "reading_page_auto_translate_selection": "Auto Translate Selection", "translate_error": "Translate E<PERSON>r", "reading_page_summary_the_chapter": "Summary the chapter", "reading_page_summary_previous_content": "Summary the previous content", "reading_page_auto_summary_previous_content": "Auto Summary Previous Content", "reading_page_at_least_two_themes": "At least 2 themes are required", "reading_page_auto_adjust_reading_theme": "Auto Adjust Reading Theme", "reading_page_auto_adjust_reading_theme_tips": "Use the 1st and 2nd colors in the color table as the day and night modes respectively", "reading_page_volume_key_turn_page": "Volume Key Turn Page", "reading_page_font_size": "Font Size", "reading_page_line_spacing": "Line Spacing", "reading_page_paragraph_spacing": "Paragraph Spacing", "reading_page_indent": "Indent", "reading_page_side_margin": "<PERSON>gin", "reading_page_top_margin": "Top Margin", "reading_page_bottom_margin": "Bottom Margin", "reading_page_font_weight": "Font Weight", "reading_page_letter_spacing": "Letter Spacing", "book_detail_save": "Save", "book_detail_edit": "Edit", "book_detail_nth_book": "{count, plural, =1{1st book you've read} =2{2nd book you've read} few{3rd book you've read} other{{count}th book you've read}}", "book_detail_last_read_date": "Last read: ", "book_detail_import_date": "Import date: ", "reading_page_reading_info": "Reading Info", "reading_page_reading_info_chapter_title": "Chapter Title", "reading_page_reading_info_battery": "Battery", "reading_page_reading_info_time": "Time", "reading_page_reading_info_battery_and_time": "Battery and Time", "reading_page_reading_info_chapter_progress": "Chapter Progress", "reading_page_reading_info_book_progress": "Book Progress", "bookshelf_dragging": "Release to import", "notes_page_detail": "Detail", "notes_page_export": "Export", "notes_page_copied": "<PERSON>pied", "notes_page_exported_to": "Exported to", "notes_page_sort_time": "By time", "notes_page_sort_chapter": "By chapter", "notes_page_filter_reset": "Reset", "notes_page_view_all_n_notes": "{count, plural, =0{No notes qualified} =1{View 1 note} other{View all {count} notes}}", "common_delete": "Delete", "common_hours_full": "{ count, plural, =1{1 hour} other{{count} hours} }", "common_hours": "{ count, plural, other{{count} h} }", "common_minutes_full": "{ count, plural, =1{1 minute} other{{count} minutes} }", "common_minutes": "{ count, plural, other{{count} m} }", "common_seconds_full": "{ count, plural, =1{1 second} other{{count} seconds} }", "common_seconds": "{ count, plural, other{{count} s} }", "common_save": "Save", "common_cancel": "Cancel", "common_ok": "OK", "common_success": "Success", "common_failed": "Failed", "common_uploading": "Uploading", "common_downloading": "Downloading", "common_copy": "Copy", "common_new_version": "New version!", "common_update": "Update", "common_no_new_version": "No new version", "common_confirm": "Confirm", "common_canceled": "Canceled", "common_attention": "Attention", "common_saving": "Saving", "common_saved": "Saved", "common_test": "Test", "common_dissolve": "Dissolve", "common_edit": "Edit", "common_nth_week": "{count, plural, =1{1st week} =2{2nd week} few{3rd week} other{{count}th week}}", "common_apply": "Apply", "common_reset": "Reset", "common_none": "None", "common_error": "Error", "common_undo": "Undo", "commom_download": "Download", "common_pause": "Pause", "common_resume": "Resume", "common_download_failed": "Download Failed", "common_retry": "Retry", "common_search": "Search", "search_books_hint": "Search books...", "common_dictionary": "Dictionary", "storage": "Storage", "storage_info": "Storage Info", "storage_total_size": "Total Size", "storage_database_file": "Database File", "storage_log_file": "Log File", "storage_cache_file": "Cache <PERSON>", "storage_data_file": "Data File", "storage_book_file": "Book File", "storage_cover_file": "Cover File", "storage_font_file": "Font File", "storage_data_file_details": "Data File Details", "storage_clear_cache": "<PERSON>ache", "service_import_success": "Import success", "service_import_n_books": "{count, plural, =1{importing 1 book} other{importing {count} books}}", "webdav_webdav_not_enabled": "WebDAV is not enabled", "webdav_syncing": "Syncing", "webdav_syncing_files": "Syncing files", "webdav_sync_complete": "Sync complete", "webdav_connection_success": "Connection success", "webdav_connection_failed": "Connection failed", "webdav_set_info_first": "Please set WebDAV information first", "webdav_choose_Sources": "Choose Sources", "webdav_download": "Download from WebDAV", "webdav_upload": "Upload to WebDAV", "webdav_only_wifi": "Only sync when WiFi is connected", "webdav_sync_aborted": "Sync aborted", "webdav_sync_aborted_content": "Failed to get data from WebDAV. Please check the following: \n1. Network connection \n2. Ensure that valid reading data has been uploaded to WebDAV on other devices", "webdav_sync_direction": "WebDAV data may be modified by other devices, please choose how to handle the data", "settings_sync": "Sync", "settings_sync_webdav": "WebDAV", "settings_sync_enable_webdav": "Enable WebDAV", "settings_sync_webdav_url": "WebDAV URL", "settings_sync_webdav_username": "Username", "settings_sync_webdav_password": "Password", "settings_sync_webdav_test_connection": "Test Connection", "settings_sync_webdav_sync_now": "Sync Now", "export_and_import": "Export/Import", "export_and_import_export": "Export", "export_and_import_import": "Import", "exporting": "Exporting", "importing": "Importing", "export_to": "Export to {path}", "import_cannot_get_file_path": "Cannot get file path", "import_success_restart_app": "Import success, please restart the app", "import_failed": "Import failed: {error}", "settings_sync_completed_toast": "Show toast when sync completed", "settings_translate": "Translate", "settings_translate_current_service": "Current Service", "settings_translate_auto": "Auto", "settings_translate_from": "From", "settings_translate_to": "To", "settings_narrate": "Narrate", "settings_narrate_voice": "Voice", "settings_narrate_voice_model": "Voice Model", "settings_narrate_voice_model_current_model": "Current Model", "settings_narrate_voice_model_not_selected": "Not Selected", "settings_narrate_voice_model_click_to_view": "Click to View", "settings_narrate_voice_model_male": "Male", "settings_narrate_voice_model_female": "Female", "settings_ai": "AI", "settings_ai_services": "Services", "settings_ai_prompt": "Prompt", "settings_ai_prompt_test": "Test AI config", "settings_ai_prompt_summary_the_chapter": "Summary the chapter", "settings_ai_prompt_summary_the_book": "Summary the book", "settings_ai_prompt_summary_the_previous_content": "Summary the previous content", "settings_ai_prompt_grammar": "Chinese Grammar Analysis", "settings_ai_cache": "<PERSON>", "settings_ai_cache_size": "<PERSON>", "settings_ai_cache_current_size": "Current {count, plural, other{{count} caches}}", "settings_ai_cache_clear": "Clear cache", "ai_service_not_configured": "Please configure the AI service in settins page.", "ai_chat": "Cha<PERSON>", "ai_hint_text": "Start chatting", "ai_hint_input_placeholder": "Input message...", "ai_hint_collapse": "Collapse", "ai_hint_expand": "Expand", "ai_quick_prompt_explain": "Explain", "ai_quick_prompt_explain_text": "Please explain", "ai_quick_prompt_opinion": "Your opinion", "ai_quick_prompt_opinion_text": "What's your opinion on this?", "ai_quick_prompt_summary": "Summarize", "ai_quick_prompt_summary_text": "Please summarize", "ai_quick_prompt_analyze": "Analyze", "ai_quick_prompt_analyze_text": "Please analyze", "ai_quick_prompt_suggest": "Suggest", "ai_quick_prompt_suggest_text": "Any suggestions?", "ai_quick_prompt_grammar": "Grammar", "ai_quick_prompt_grammar_text": "Analyze the grammar in", "settings_reading": "Reading", "settings_bookshelf": "Bookshelf", "settings_bookshelf_cover": "Book cover", "settings_bookshelf_cover_width": "Book cover width", "ai_cached_by": "Cached by {service}", "ai_regenerate": "Regenerate", "storage_permission_denied": "Storage permission denied", "gallery_permission_denied": "Gallery permission denied", "goto_authorize": "Go to authorize", "settings_advanced": "Advanced", "settings_advanced_log": "Log", "settings_advanced_log_clear_log": "Clear Log", "settings_advanced_log_export_log": "Export Log", "settings_advanced_clear_log_when_start": "Clear log when start", "about_check_for_updates": "Check for updates", "update_new_version": "New version: ", "update_current_version": "Current version: ", "about_privacy_policy": "Privacy Policy", "about_terms_of_use": "Terms of Use", "tts_volume": "Volume", "tts_rate": "Rate", "tts_pitch": "Pitch", "tts_narrator": "Narrator", "tts_stop_after": "{count, plural, =0{No timer} =1{Stop in 1 minute} other{stop after {count} minutes}}", "tts_type": "TTS Type", "tts_type_internal": "Internal TTS", "tts_type_system": "System TTS", "statistic_just_now": "just now", "statistic_minutes_ago": "{count, plural, =1{1 minute ago} other{{count} minutes ago}}", "statistic_hours_ago": "{count, plural, =1{1 hour ago} other{{count} hours ago}}", "statistic_yesterday": "{count, plural, =1{1 yesterday} other{{count} yesterday}}", "statistic_days_ago": "{count, plural, =1{1 day ago} other{{count} days ago}}", "statistic_months_ago": "{count, plural, =1{1 month ago} other{{count} months ago}}", "statistic_years_ago": "{count, plural, =1{1 year ago} other{{count} years ago}}", "statistic_deleted_records": "Records to be deleted", "statistic_deleted_records_tips": "Will be deleted when leaving this page", "webview_unsupported_version": "Unsupported Webview Version", "webview_unsupported_message": "Your Webview version may be not supported. Current version is {version}, required minimum version is {minVersion}, please update your Webview", "webview_update": "How to update", "webview_cancel": "I know", "webview2_not_installed": "Failed to find an installed WebView2 Runtime or non-stable Microsoft Edge installation.", "webview2_install": "Go to diownload", "iap_page_title": "Activate Product", "iap_page_restore": "Restore Purchase", "iap_page_why_choose": "Why Choose An<PERSON>", "iap_page_feature_ai": "AI Reading", "iap_page_feature_ai_desc": "AI-powered summaries, Q&A for effortless reading", "iap_page_feature_sync": "Cross-platform Sync", "iap_page_feature_sync_desc": "Keep your progress and data in sync", "iap_page_feature_stats": "Detailed Statistics", "iap_page_feature_stats_desc": "Reading time, heatmap and more", "iap_page_feature_custom": "Customize Everything", "iap_page_feature_custom_desc": "Font, color, size, spacing and more", "iap_page_feature_note": "Powerful Notes", "iap_page_feature_note_desc": "Categorize, filter, sort, export to md, csv, txt and more", "iap_page_feature_rich": "Rich <PERSON>", "iap_page_feature_rich_desc": "Notes, narration, translation", "iap_page_restore_hint": "If you change device or reinstall, click 'Restore Purchase' in the top right corner.\nOnly valid for devices under the current Apple ID", "iap_page_one_time_purchase": "Continue", "iap_page_lifetime_hint": "One-time payment, {price} lifetime access", "iap_page_status_purchased": "Thank you for your support! Your product has been successfully activated", "iap_page_status_trial": "You have {days} days left in your trial", "iap_page_status_trial_expired": "Purchase lifetime version to continue reading", "iap_page_status_original": "You are our early user, thank you for your support!", "iap_page_status_unknown": "Unable to determine your activation status", "iap_page_date_purchased": "Purchase date: {date}", "iap_page_date_trial_start": "Trial started: {date}", "iap_page_date_original": "Join date: {date}", "iap_status_purchased": "Lifetime Premium User", "iap_status_trial": "Trial Period", "iap_status_trial_expired": "Trial Expired", "iap_status_original": "Early User", "iap_status_unknown": "Unknown", "download_fonts": "Download Fonts", "font_no_available_fonts": "No available fonts", "font_official_website": "Official Website", "font_license_agreement": "License Agreement", "font_downloaded": "Downloaded", "font_failed_to_load_fonts": "Failed to load fonts", "font_downloading": "Downloading {progress}", "font_cancelled": "Cancelled {progress}", "book_cover_remove_success": "Book cover removed successfully", "book_cover_remove_failed": "Failed to remove book cover", "reading_page_search_tooltip": "Open Search Page", "reading_page_clear_search_tooltip": "Clear Search Results", "reading_page_free_selection_mode": "Free Selection", "reading_page_segmentation_mode": "Word Segmentation", "reading_page_switch_to_free_selection": "Switch to free text selection mode", "reading_page_switch_to_segmentation": "Switch to word segmentation selection mode", "reading_bookmark": "Bookmark", "no_bookmarks": "No Bookmarks", "no_bookmarks_tip": "Pull down or click the bookmark icon in the top-right corner to add a bookmark.", "note_list_show_bookmark": "Show Bookmark"}