import 'package:flutter/material.dart';
import 'package:icons_plus/icons_plus.dart';
import 'design_system.dart';
import 'color_system.dart';

/// Comprehensive Icon System for the Dasso Reader App
///
/// This class provides a consistent iconography system that ensures:
/// - 🎨 Stylistic consistency (outlined icons as primary style)
/// - 📏 Standardized sizing across all contexts
/// - 🎯 Semantic meaning and recognizability
/// - 📱 Crisp rendering on high-resolution screens
/// - ♿ Accessibility compliance with proper sizing
class AppIcons {
  // Private constructor to prevent instantiation
  AppIcons._();

  // =====================================================
  // ICON STYLE STANDARDS
  // =====================================================

  /// Primary icon style: Outlined icons for consistency
  /// This ensures a cohesive visual language across the app
  static const IconStyle primaryStyle = IconStyle.outlined;

  /// Secondary icon style: Filled icons for emphasis
  /// Used sparingly for active states or primary actions
  static const IconStyle secondaryStyle = IconStyle.filled;

  // =====================================================
  // ICON SIZE STANDARDS
  // =====================================================

  /// Extra small icons (16px) - for dense UI, badges, indicators
  static const double sizeXS = DesignSystem.widgetIconSizeSmall; // 16.0

  /// Small icons (20px) - for compact buttons, list items
  static const double sizeS = 20.0;

  /// Medium icons (28px) - standard size for most UI elements, updated for TabBar navigation
  static const double sizeM =
      DesignSystem.widgetIconSizeMedium; // 28.0 (increased from 24.0)

  /// Large icons (32px) - for prominent buttons, headers
  static const double sizeL = DesignSystem.widgetIconSizeLarge; // 32.0

  /// Extra large icons (48px) - for hero elements, main actions
  static const double sizeXL = 48.0;

  /// Double extra large icons (64px) - for splash screens, empty states
  static const double sizeXXL = 64.0;

  // =====================================================
  // NAVIGATION ICONS
  // =====================================================

  /// Home/Dashboard navigation
  static const IconData home = Icons.home_outlined;
  static const IconData homeFilled = Icons.home;

  /// Search functionality
  static const IconData search = Icons.search_outlined;
  static const IconData searchFilled = Icons.search;

  /// Settings and preferences
  static const IconData settings = Icons.settings_outlined;
  static const IconData settingsFilled = Icons.settings;

  /// Menu/hamburger navigation
  static const IconData menu = Icons.menu_outlined;
  static const IconData menuFilled = Icons.menu;

  /// Back navigation
  static const IconData back = Icons.arrow_back_outlined;
  static const IconData backFilled = Icons.arrow_back;

  /// Close/dismiss
  static const IconData close = Icons.close_outlined;
  static const IconData closeFilled = Icons.close;

  // =====================================================
  // ACTION ICONS
  // =====================================================

  /// Add/create new content
  static const IconData add = Icons.add_outlined;
  static const IconData addFilled = Icons.add;

  /// Edit/modify content
  static const IconData edit = Icons.edit_outlined;
  static const IconData editFilled = Icons.edit;

  /// Delete/remove content
  static const IconData delete = Icons.delete_outline;
  static const IconData deleteFilled = Icons.delete;

  /// Save content
  static const IconData save = Icons.save_outlined;
  static const IconData saveFilled = Icons.save;

  /// Share content
  static const IconData share = Icons.share_outlined;
  static const IconData shareFilled = Icons.share;

  /// Copy content
  static const IconData copy = Icons.copy_outlined;
  static const IconData copyFilled = Icons.copy;

  /// Download content
  static const IconData download = Icons.download_outlined;
  static const IconData downloadFilled = Icons.download;

  /// Upload content
  static const IconData upload = Icons.upload_outlined;
  static const IconData uploadFilled = Icons.upload;

  // =====================================================
  // READING & LEARNING ICONS
  // =====================================================

  /// Book/reading content
  static const IconData book = Icons.book_outlined;
  static const IconData bookFilled = Icons.book;

  /// Bookmark content
  static const IconData bookmark = Icons.bookmark_outline;
  static const IconData bookmarkFilled = Icons.bookmark;

  /// Highlight text
  static const IconData highlight = AntDesign.highlight_outline;
  static const IconData highlightFilled = AntDesign.highlight_fill;

  /// Underline text
  static const IconData underline = Icons.format_underlined_outlined;
  static const IconData underlineFilled = Icons.format_underlined;

  /// Notes/annotations
  static const IconData note = Icons.note_outlined;
  static const IconData noteFilled = Icons.note;

  /// Dictionary/translation
  static const IconData dictionary = Icons.translate_outlined;
  static const IconData dictionaryFilled = Icons.translate;

  /// Audio/pronunciation
  static const IconData audio = Icons.volume_up_outlined;
  static const IconData audioFilled = Icons.volume_up;

  /// Audio muted/disabled
  static const IconData audioOff = Icons.volume_off_outlined;
  static const IconData audioOffFilled = Icons.volume_off;

  // =====================================================
  // STATUS & FEEDBACK ICONS
  // =====================================================

  /// Success/completion
  static const IconData success = Icons.check_circle_outline;
  static const IconData successFilled = Icons.check_circle;

  /// Error/failure
  static const IconData error = Icons.error_outline;
  static const IconData errorFilled = Icons.error;

  /// Warning/caution
  static const IconData warning = Icons.warning_amber_outlined;
  static const IconData warningFilled = Icons.warning_amber;

  /// Information
  static const IconData info = Icons.info_outline;
  static const IconData infoFilled = Icons.info;

  /// Loading/progress
  static const IconData loading = Icons.refresh_outlined;
  static const IconData loadingFilled = Icons.refresh;

  // =====================================================
  // MEDIA & PLAYBACK ICONS
  // =====================================================

  /// Play media
  static const IconData play = Icons.play_arrow_outlined;
  static const IconData playFilled = Icons.play_arrow;

  /// Pause media
  static const IconData pause = Icons.pause_outlined;
  static const IconData pauseFilled = Icons.pause;

  /// Stop media
  static const IconData stop = Icons.stop_outlined;
  static const IconData stopFilled = Icons.stop;

  /// Next track/page
  static const IconData next = Icons.skip_next_outlined;
  static const IconData nextFilled = Icons.skip_next;

  /// Previous track/page
  static const IconData previous = Icons.skip_previous_outlined;
  static const IconData previousFilled = Icons.skip_previous;

  // =====================================================
  // COMMUNICATION ICONS
  // =====================================================

  /// Chat/messaging
  static const IconData chat = EvaIcons.message_circle_outline;
  static const IconData chatFilled = Icons.chat;

  /// AI assistant
  static const IconData ai = Icons.smart_toy_outlined;
  static const IconData aiFilled = Icons.smart_toy;

  /// Email/contact
  static const IconData email = Icons.email_outlined;
  static const IconData emailFilled = Icons.email;

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  /// Get responsive icon size based on context and screen size
  static double getResponsiveSize(
    BuildContext context,
    IconSize size, {
    bool isAdaptive = true,
  }) {
    double baseSize;
    switch (size) {
      case IconSize.xs:
        baseSize = sizeXS;
        break;
      case IconSize.s:
        baseSize = sizeS;
        break;
      case IconSize.m:
        baseSize = sizeM;
        break;
      case IconSize.l:
        baseSize = sizeL;
        break;
      case IconSize.xl:
        baseSize = sizeXL;
        break;
      case IconSize.xxl:
        baseSize = sizeXXL;
        break;
    }

    return isAdaptive
        ? DesignSystem.getAdaptiveIconSize(context, baseSize)
        : baseSize;
  }

  /// Create a themed icon with consistent styling
  static Widget themed(
    BuildContext context,
    IconData iconData, {
    IconSize size = IconSize.m,
    IconType type = IconType.primary,
    Color? color,
    String? semanticsLabel,
    bool isAdaptive = true,
  }) {
    final iconSize = getResponsiveSize(context, size, isAdaptive: isAdaptive);
    final iconColor = color ?? ColorSystem.getIconColor(context, type);

    return Icon(
      iconData,
      size: iconSize,
      color: iconColor,
      semanticLabel: semanticsLabel,
    );
  }

  /// Create an icon button with consistent styling
  static Widget button(
    BuildContext context,
    IconData iconData, {
    required VoidCallback? onPressed,
    IconSize size = IconSize.m,
    IconType type = IconType.primary,
    Color? color,
    String? tooltip,
    bool isAdaptive = true,
  }) {
    final iconSize = getResponsiveSize(context, size, isAdaptive: isAdaptive);
    final iconColor = color ?? ColorSystem.getIconColor(context, type);

    return IconButton(
      onPressed: onPressed,
      icon: Icon(
        iconData,
        size: iconSize,
        color: iconColor,
      ),
      tooltip: tooltip,
      constraints: BoxConstraints(
        minWidth: iconSize + 24, // Ensure minimum touch target
        minHeight: iconSize + 24,
      ),
    );
  }

  /// Get icon style preference (outlined vs filled)
  static IconData getStyledIcon(
    IconData outlined,
    IconData filled, {
    bool isActive = false,
    IconStyle? preferredStyle,
  }) {
    final style = preferredStyle ?? (isActive ? secondaryStyle : primaryStyle);
    return style == IconStyle.filled ? filled : outlined;
  }
}

// =====================================================
// ENUMERATIONS
// =====================================================

/// Icon size enumeration for consistent sizing
enum IconSize {
  xs, // 16px - Extra small
  s, // 20px - Small
  m, // 28px - Medium (default) - Updated for TabBar navigation
  l, // 32px - Large
  xl, // 48px - Extra large
  xxl, // 64px - Double extra large
}

/// Icon style enumeration for consistent styling
enum IconStyle {
  outlined, // Primary style
  filled, // Secondary style for emphasis
}
