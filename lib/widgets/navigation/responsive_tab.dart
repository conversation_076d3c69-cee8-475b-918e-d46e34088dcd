import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'dart:math' as math;

/// A production-ready responsive tab widget that adapts to device characteristics
///
/// This widget automatically adjusts spacing, sizing, and layout based on:
/// - Device pixel ratio (screen density)
/// - Text scaling factor (accessibility settings)
/// - Device size category (phone, tablet, etc.)
/// - Manufacturer-specific considerations
///
/// Maintains full compatibility with existing dasso-reader design system
/// and preserves all current visual effects and animations.
class ResponsiveTab extends StatelessWidget {
  const ResponsiveTab({
    super.key,
    required this.text,
    required this.icon,
    required this.isSelected,
    required this.colorScheme,
    this.minHeight = 48.0, // Accessibility minimum
    this.enableDebugLogging = false,
  });

  final String text;
  final Widget icon;
  final bool isSelected;
  final ColorScheme colorScheme;
  final double minHeight;
  final bool enableDebugLogging;

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final devicePixelRatio = mediaQuery.devicePixelRatio;
    final textScaler = mediaQuery.textScaler;

    // Calculate adaptive dimensions
    final adaptiveHeight =
        _calculateAdaptiveHeight(context, devicePixelRatio, textScaler);
    final adaptiveSpacing =
        _calculateAdaptiveSpacing(context, devicePixelRatio);
    final adaptiveFontSize = _calculateAdaptiveFontSize(context, textScaler);

    // Debug logging for development (only in debug mode)
    if (enableDebugLogging && kDebugMode) {
      final deviceInfo = DesignSystem.getManufacturerDebugInfo();
      AnxLog.info('🎯 ResponsiveTab Debug - Text: $text');
      AnxLog.info('  📱 Device: ${DesignSystem.getDeviceDescription()}');
      AnxLog.info('  📊 Device Pixel Ratio: $devicePixelRatio');
      AnxLog.info('  🔤 Text Scale Factor: ${textScaler.scale(1.0)}');
      AnxLog.info('  📏 Adaptive Height: $adaptiveHeight');
      AnxLog.info('  📐 Adaptive Spacing: $adaptiveSpacing');
      AnxLog.info('  🔠 Adaptive Font Size: $adaptiveFontSize');
      AnxLog.info('  🏭 Manufacturer Adjustments:');
      AnxLog.info(
          '    - Spacing Multiplier: ${deviceInfo['spacingMultiplier']}');
      AnxLog.info(
          '    - Font Weight Multiplier: ${deviceInfo['fontWeightMultiplier']}');
      AnxLog.info(
          '    - Icon Size Multiplier: ${deviceInfo['iconSizeMultiplier']}');
      AnxLog.info(
          '    - Elevation Multiplier: ${deviceInfo['elevationMultiplier']}');
      AnxLog.info(
          '  ✅ Reference Device: ${DesignSystem.isReferenceDevice() ? 'YES (Pixel 9 Pro)' : 'NO'}');

      // Validate layout for cross-device consistency
      TabDeviceUtils.validateTabLayout(
        context,
        calculatedHeight: adaptiveHeight,
        calculatedSpacing: adaptiveSpacing,
        calculatedFontSize: adaptiveFontSize,
      );
    }

    // Enhanced semantic information for screen readers
    final semanticLabel = _buildSemanticLabel(context);
    final semanticHint = _buildSemanticHint(context);

    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      button: true,
      selected: isSelected,
      enabled: true,
      // Announce state changes for screen readers
      liveRegion: isSelected,
      child: SizedBox(
        height: adaptiveHeight,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Adaptive icon with density scaling - preserves existing gradient effect
            _buildAdaptiveIcon(context, devicePixelRatio),
            SizedBox(height: adaptiveSpacing),
            // Adaptive text with proper scaling - maintains existing styling
            _buildAdaptiveText(context, adaptiveFontSize),
          ],
        ),
      ),
    );
  }

  /// Calculate adaptive height based on device characteristics
  double _calculateAdaptiveHeight(
      BuildContext context, double devicePixelRatio, TextScaler textScaler) {
    const baseHeight = 74.0; // Updated to match Material Design standard

    // Adjust for device density (normalize to 3.0 DPI)
    double densityFactor = (devicePixelRatio / 3.0).clamp(0.85, 1.25);

    // Adjust for text scaling with reasonable bounds
    double textScaleFactor = textScaler.scale(1.0).clamp(0.9, 1.4);

    // Adjust for device type
    double deviceFactor = 1.0;
    if (DesignSystem.isSmallPhone(context)) {
      deviceFactor = 0.9; // Slightly smaller on small phones
    } else if (DesignSystem.isTablet(context)) {
      deviceFactor = 1.1; // Slightly larger on tablets
    }

    // Calculate final height
    double calculatedHeight =
        baseHeight * densityFactor * textScaleFactor * deviceFactor;

    // Ensure accessibility compliance (minimum 48dp touch target)
    return math.max(calculatedHeight, minHeight);
  }

  /// Calculate adaptive spacing between icon and text
  /// Enhanced with text width compensation for pixel-perfect consistency
  double _calculateAdaptiveSpacing(
      BuildContext context, double devicePixelRatio) {
    const baseSpacing = 6.0; // Increased to match reference standard

    // Scale spacing based on device density
    double densityFactor = (devicePixelRatio / 3.0).clamp(0.8, 1.2);

    // Manufacturer-specific adjustments for pixel-perfect consistency
    densityFactor *=
        DesignSystem.getAdaptiveSpacingWithManufacturerAdjustment(context, 1.0);

    // Apply text width compensation to maintain visual balance
    // This compensates for text width changes caused by font weight adjustments
    densityFactor *= DesignSystem.getTextWidthCompensationMultiplier();

    // Adjust for device size
    if (DesignSystem.isSmallPhone(context)) {
      densityFactor *= 0.8; // Tighter spacing on small phones
    } else if (DesignSystem.isTablet(context)) {
      densityFactor *= 1.1; // More generous spacing on tablets
    }

    return baseSpacing * densityFactor;
  }

  /// Calculate adaptive font size with accessibility considerations
  double _calculateAdaptiveFontSize(
      BuildContext context, TextScaler textScaler) {
    const baseFontSize = 14.0; // Increased from 12.0 to 14.0

    // Apply text scaling with constraints to prevent overflow
    double scaledSize = textScaler.scale(baseFontSize);

    // Constrain to reasonable bounds for tab labels (proportionally increased)
    double constrainedSize =
        scaledSize.clamp(12.0, 18.0); // Increased from 10.0-16.0 to 12.0-18.0

    // Slight adjustment for device size
    if (DesignSystem.isSmallPhone(context)) {
      constrainedSize =
          math.min(constrainedSize, 16.0); // Increased from 14.0 to 16.0
    }

    return constrainedSize;
  }

  /// Build adaptive icon with proper sizing and effects
  /// Enhanced with manufacturer-specific adjustments for pixel-perfect consistency
  Widget _buildAdaptiveIcon(BuildContext context, double devicePixelRatio) {
    // Calculate adaptive icon size with manufacturer-specific adjustments
    const baseIconSize = 28.0; // Increased from 24.0 to 28.0
    double adaptiveIconSize =
        baseIconSize * (devicePixelRatio / 3.0).clamp(0.9, 1.2);

    // Apply manufacturer-specific icon size adjustments for pixel-perfect consistency
    adaptiveIconSize = DesignSystem.getAdjustedIconSize(adaptiveIconSize);

    // Ensure icon doesn't get too large on high-density screens (proportionally increased)
    adaptiveIconSize =
        math.min(adaptiveIconSize, 32.0); // Increased from 28.0 to 32.0

    return SizedBox(
      width: adaptiveIconSize,
      height: adaptiveIconSize,
      child: FittedBox(
        fit: BoxFit.contain,
        child: isSelected
            ? ShaderMask(
                blendMode: BlendMode.srcIn,
                shaderCallback: (bounds) => LinearGradient(
                  colors: [
                    Theme.of(context).tabBarTheme.labelColor ??
                        colorScheme.primary,
                    Theme.of(context).tabBarTheme.labelColor ??
                        colorScheme.tertiary,
                  ],
                ).createShader(bounds),
                child: icon,
              )
            : IconTheme(
                data: IconThemeData(
                  color: Theme.of(context).tabBarTheme.unselectedLabelColor ??
                      colorScheme.onSurfaceVariant,
                ),
                child: icon,
              ),
      ),
    );
  }

  /// Build adaptive text with proper styling and constraints
  /// Enhanced with manufacturer-specific adjustments integrated into design system
  Widget _buildAdaptiveText(BuildContext context, double fontSize) {
    // Use design system for consistent font sizing
    final adjustedFontSize = fontSize;

    // Calculate manufacturer-aware font weight using design system for pixel-perfect consistency
    final baseFontWeight = isSelected ? FontWeight.w600 : FontWeight.w500;

    // Apply manufacturer-specific font weight adjustments for pixel-perfect text rendering
    // Pass the text for character-specific adjustments (Latin vs Chinese)
    final adjustedFontWeight =
        DesignSystem.getAdjustedFontWeight(baseFontWeight, text: text);

    // Debug character-specific font weight adjustments
    if (enableDebugLogging) {
      final isLatin = DesignSystem.isLatinText(text);
      final fontWeightMultiplier =
          DesignSystem.getFontWeightMultiplier(text: text);
      AnxLog.info('🔤 Character Analysis for "$text":');
      AnxLog.info('  📝 Is Latin Text: $isLatin');
      AnxLog.info(
          '  ⚖️ Font Weight Multiplier: ${fontWeightMultiplier.toStringAsFixed(2)}x');
      AnxLog.info('  🎯 Base Font Weight: ${baseFontWeight.toString()}');
      AnxLog.info(
          '  🎯 Adjusted Font Weight: ${adjustedFontWeight.toString()}');
    }

    return Flexible(
      child: SizedBox(
        // Fixed width to ensure consistent scaling behavior
        width: double.infinity,
        child: FittedBox(
          fit: BoxFit.scaleDown,
          child: ConstrainedBox(
            // Force all text to use the same baseline width for consistent scaling
            constraints: BoxConstraints(
              minWidth: _getBaselineTextWidth(adjustedFontSize),
              maxWidth: _getBaselineTextWidth(adjustedFontSize),
            ),
            child: Text(
              text,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: adjustedFontSize,
                letterSpacing: _getOptimalLetterSpacing(adjustedFontSize),
                fontWeight: adjustedFontWeight,
                height: 1.0, // Tight line height for tabs
                // Use TabBar theme colors
                color: isSelected
                    ? Theme.of(context).tabBarTheme.labelColor ??
                        colorScheme.primary
                    : Theme.of(context).tabBarTheme.unselectedLabelColor ??
                        colorScheme.onSurfaceVariant,
                // Ensure consistent rendering across devices
                textBaseline: TextBaseline.alphabetic,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Calculate optimal letter spacing based on font size for cross-device consistency
  double _getOptimalLetterSpacing(double fontSize) {
    // Adjusted for larger base font sizes (14.0px base instead of 12.0px)
    if (fontSize <= 13.0) {
      return -0.1; // Very tight for small text (adjusted threshold)
    } else if (fontSize <= 15.0) {
      return -0.2; // Standard tight spacing (adjusted threshold)
    } else {
      return -0.15; // Slightly looser for larger text
    }
  }

  /// Calculate baseline text width for consistent FittedBox scaling
  /// This ensures all text labels scale uniformly regardless of their actual length
  /// Enhanced with manufacturer-specific font weight compensation to prevent truncation
  double _getBaselineTextWidth(double fontSize) {
    // Use the width of a medium-length reference text (like "Dictionary")
    // This ensures consistent scaling behavior for all tab labels
    const referenceText = "Dictionary"; // 10 characters - representative length
    final letterSpacing = _getOptimalLetterSpacing(fontSize);

    // Approximate character width calculation
    // This is a rough estimate that works well for consistent scaling
    final approximateCharWidth =
        fontSize * 0.6; // Typical character width ratio
    final totalLetterSpacing = letterSpacing * (referenceText.length - 1);

    // Calculate base width
    final baseWidth =
        (approximateCharWidth * referenceText.length) + totalLetterSpacing;

    // Apply manufacturer-specific font weight compensation to prevent truncation
    // This accounts for text becoming wider when font weight multipliers are applied
    final fontWeightMultiplier =
        DesignSystem.getFontWeightMultiplier(text: referenceText);

    // Calculate width compensation factor
    // Font weight increases can make text 2-8% wider depending on the multiplier
    final widthCompensationFactor = fontWeightMultiplier > 1.0
        ? 1.0 +
            ((fontWeightMultiplier - 1.0) *
                0.6) // 60% compensation for width increase
        : 1.0; // No compensation needed for weight reduction

    // Add safety margin for edge cases (especially for "Vocabulary" which matches baseline length)
    final safetyMargin = 1.02; // 2% safety margin

    final finalWidth = baseWidth * widthCompensationFactor * safetyMargin;

    // Debug logging for baseline width calculation (especially useful for "Vocabulary" truncation debugging)
    if (enableDebugLogging && kDebugMode) {
      AnxLog.info('📏 Baseline Width Calculation for "$text":');
      AnxLog.info('  📐 Base Width: ${baseWidth.toStringAsFixed(2)}');
      AnxLog.info(
          '  ⚖️ Font Weight Multiplier: ${fontWeightMultiplier.toStringAsFixed(3)}x');
      AnxLog.info(
          '  📊 Width Compensation Factor: ${widthCompensationFactor.toStringAsFixed(3)}x');
      AnxLog.info('  🛡️ Safety Margin: ${safetyMargin.toStringAsFixed(3)}x');
      AnxLog.info('  🎯 Final Width: ${finalWidth.toStringAsFixed(2)}');
      if (text == "Vocabulary") {
        AnxLog.info(
            '  🔍 VOCABULARY SPECIFIC: This should prevent truncation on Huawei devices');
      }
    }

    return finalWidth;
  }

  /// Build comprehensive semantic label for screen readers
  /// Provides context-aware information about tab state and function
  String _buildSemanticLabel(BuildContext context) {
    final baseLabel = text;
    final stateInfo = isSelected ? 'selected' : 'not selected';

    // Add contextual information based on tab type
    String contextInfo = '';
    switch (text.toLowerCase()) {
      case 'bookshelf':
      case '书架':
        contextInfo = 'View your book library';
        break;
      case 'dictionary':
      case '词典':
        contextInfo = 'Look up words and definitions';
        break;
      case 'vocabulary':
      case '词汇':
        contextInfo = 'Practice vocabulary words';
        break;
      case 'hsk':
        contextInfo = 'HSK test preparation';
        break;
      case 'notes':
      case '笔记':
        contextInfo = 'View your reading notes';
        break;
      default:
        contextInfo = 'Navigate to $text section';
    }

    return '$baseLabel tab, $stateInfo. $contextInfo';
  }

  /// Build semantic hint for screen readers
  /// Provides additional context about tab interaction
  String _buildSemanticHint(BuildContext context) {
    if (isSelected) {
      return 'Currently viewing this section. Double tap to refresh or access options.';
    } else {
      return 'Double tap to switch to this section.';
    }
  }
}

/// Extension methods for device-specific tab calculations
extension ResponsiveTabExtensions on DesignSystem {
  /// Get density-aware spacing specifically for tabs
  static double getAdaptiveTabSpacing(BuildContext context) {
    final devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
    const baseSpacing = DesignSystem.spaceXS; // 4.0

    // Density-based scaling with manufacturer considerations
    double densityFactor = (devicePixelRatio / 3.0).clamp(0.8, 1.3);

    // Device-specific adjustments could be added here
    // For example, if detecting specific manufacturers that need adjustments

    return baseSpacing * densityFactor;
  }

  /// Get density-aware tab height
  static double getAdaptiveTabHeight(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final devicePixelRatio = mediaQuery.devicePixelRatio;
    final textScaler = mediaQuery.textScaler;

    const baseHeight = 74.0; // Updated to match Material Design standard
    const minHeight = 48.0; // Accessibility minimum

    // Scale for device density
    double densityFactor = (devicePixelRatio / 3.0).clamp(0.85, 1.25);

    // Scale for text size
    double textFactor = textScaler.scale(1.0).clamp(0.9, 1.3);

    // Device-specific adjustments
    double deviceFactor = 1.0;
    if (DesignSystem.isSmallPhone(context)) {
      deviceFactor = 0.9;
    } else if (DesignSystem.isTablet(context)) {
      deviceFactor = 1.1;
    }

    double calculatedHeight =
        baseHeight * densityFactor * textFactor * deviceFactor;

    // Ensure accessibility compliance
    return math.max(calculatedHeight, minHeight);
  }
}

/// Utility class for device detection and tab-specific adjustments
/// Enhanced with professional manufacturer detection for pixel-perfect consistency
class TabDeviceUtils {
  /// Check if device needs special spacing adjustments
  static bool needsSpacingAdjustment() {
    // All devices benefit from manufacturer-specific adjustments
    return true;
  }

  /// Get manufacturer-specific spacing multiplier using design system integration
  static double getManufacturerSpacingMultiplier() {
    // Now integrated into DesignSystem for consistency
    return 1.0; // Handled by DesignSystem.getAdaptiveSpacingWithManufacturerAdjustment
  }

  /// Validate tab layout for cross-device consistency
  /// Enhanced with manufacturer detection for professional debugging
  static void validateTabLayout(
    BuildContext context, {
    required double calculatedHeight,
    required double calculatedSpacing,
    required double calculatedFontSize,
  }) {
    if (kDebugMode) {
      final deviceInfo = MediaQuery.of(context);
      final devicePixelRatio = deviceInfo.devicePixelRatio;
      final screenWidth = deviceInfo.size.width;
      final manufacturerInfo = DesignSystem.getManufacturerDebugInfo();

      // Log comprehensive device characteristics for debugging
      debugPrint('🔍 === Professional Tab Layout Validation ===');
      debugPrint('📱 Device: ${DesignSystem.getDeviceDescription()}');
      debugPrint('📊 Device Pixel Ratio: $devicePixelRatio');
      debugPrint('📐 Screen Width: $screenWidth');
      debugPrint('📏 Calculated Height: $calculatedHeight');
      debugPrint('📐 Calculated Spacing: $calculatedSpacing');
      debugPrint('🔠 Calculated Font Size: $calculatedFontSize');
      debugPrint('🏭 Manufacturer Multipliers:');
      debugPrint('   - Spacing: ${manufacturerInfo['spacingMultiplier']}');
      debugPrint(
          '   - Font Weight: ${manufacturerInfo['fontWeightMultiplier']}');
      debugPrint(
          '   - Text Width Compensation: ${manufacturerInfo['textWidthCompensationMultiplier']}');
      debugPrint('   - Icon Size: ${manufacturerInfo['iconSizeMultiplier']}');
      debugPrint('   - Elevation: ${manufacturerInfo['elevationMultiplier']}');
      debugPrint(
          '🎯 Reference Device: ${DesignSystem.isReferenceDevice() ? 'YES (Pixel 9 Pro Standard)' : 'NO (Adjusted for consistency)'}');

      // Validate ranges for consistency (updated for larger base sizes)
      assert(calculatedHeight >= 48.0 && calculatedHeight <= 100.0,
          'Tab height out of acceptable range: $calculatedHeight');
      assert(calculatedSpacing >= 3.0 && calculatedSpacing <= 12.0,
          'Tab spacing out of acceptable range: $calculatedSpacing');
      assert(calculatedFontSize >= 12.0 && calculatedFontSize <= 18.0,
          'Font size out of acceptable range: $calculatedFontSize'); // Updated from 10.0-16.0 to 12.0-18.0

      debugPrint('✅ Professional tab layout validation passed');
      debugPrint('🎯 Pixel-perfect consistency achieved');
      debugPrint('================================================');
    }
  }
}
