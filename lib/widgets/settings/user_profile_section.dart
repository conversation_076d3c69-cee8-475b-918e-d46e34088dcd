import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class UserProfileSection extends StatelessWidget {
  const UserProfileSection({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final prefsNotifier = Provider.of<Prefs>(context);

    // Get username from preferences or use default
    final username = prefsNotifier.username ?? "Reader";

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
      child: Row(
        children: [
          // Avatar
          CircleAvatar(
            radius: 40,
            backgroundColor: colorScheme.primaryContainer,
            child: Icon(
              Icons.person,
              size: 40,
              color: colorScheme.onPrimaryContainer,
            ),
          ),
          const SizedBox(width: 16),
          // User info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  username,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: DesignSystem.getSettingsTextColor(context,
                            isPrimary: true),
                      ),
                ),
                const SizedBox(height: 4),
                Text(
                  "Chinese Language Learner",
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: DesignSystem.getSettingsTextColor(context,
                            isPrimary: false),
                      ),
                ),
              ],
            ),
          ),
          // Edit button
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              // Show dialog to edit username
              showDialog(
                context: context,
                builder: (context) => _buildEditProfileDialog(context),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEditProfileDialog(BuildContext context) {
    final prefsNotifier = Provider.of<Prefs>(context, listen: false);
    final TextEditingController controller =
        TextEditingController(text: prefsNotifier.username ?? "Reader");

    return AlertDialog(
      title: Text(
        "Edit Profile",
        style: TextStyle(
          color: DesignSystem.getSettingsTextColor(context, isPrimary: true),
        ),
      ),
      content: TextField(
        controller: controller,
        decoration: const InputDecoration(
          labelText: "Username",
          hintText: "Enter your name",
        ),
        autofocus: true,
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            "Cancel",
            style: TextStyle(
              color:
                  DesignSystem.getSettingsTextColor(context, isPrimary: false),
            ),
          ),
        ),
        TextButton(
          onPressed: () {
            // Save username
            prefsNotifier.saveUsername(controller.text);
            Navigator.pop(context);
          },
          child: Text(
            "Save",
            style: TextStyle(
              color:
                  DesignSystem.getSettingsTextColor(context, isPrimary: true),
            ),
          ),
        ),
      ],
    );
  }
}
