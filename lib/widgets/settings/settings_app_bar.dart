import 'package:flutter/material.dart';

AppBar settingsAppBar(String title, BuildContext context) {
  final theme = Theme.of(context);
  return AppBar(
    leading: Icon<PERSON>utton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        Navigator.pop(context);
      },
    ),
    title: Text(title),
    backgroundColor: theme.appBarTheme.backgroundColor,
    foregroundColor: theme.appBarTheme.foregroundColor,
  );
}
