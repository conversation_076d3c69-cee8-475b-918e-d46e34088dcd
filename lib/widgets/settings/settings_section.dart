import 'package:dasso_reader/widgets/settings/settings_tile.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:flutter/material.dart';

abstract class AbstractSettingsSection extends StatelessWidget {
  const AbstractSettingsSection({super.key});
}

class SettingsSection extends AbstractSettingsSection {
  const SettingsSection({
    super.key,
    required this.tiles,
    this.margin,
    this.title,
  });

  final List<AbstractSettingsTile> tiles;
  final EdgeInsetsDirectional? margin;
  final Widget? title;

  @override
  Widget build(BuildContext context) {
    return buildSectionBody(context);
  }

  Widget buildSectionBody(BuildContext context) {
    final tileList = buildTileList();

    if (title == null) {
      return tileList;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsetsDirectional.only(
            top: DesignSystem.spaceS * 1.5, // 12.0 (equivalent to 24 * 0.5)
            bottom: DesignSystem.spaceXS * 1.25, // 5.0 (equivalent to 10 * 0.5)
            start: DesignSystem.spaceL, // 24.0 (preserves exact spacing)
            end: DesignSystem.spaceL, // 24.0 (preserves exact spacing)
          ),
          child: DefaultTextStyle(
            style: TextStyle(
              color:
                  DesignSystem.getSettingsTextColor(context, isPrimary: true),
              fontWeight: FontWeight.w600,
            ),
            child: title!,
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(
              horizontal: DesignSystem.spaceS), // 8.0 (preserves exact spacing)
          child: Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerLow,
              borderRadius: BorderRadius.circular(
                  DesignSystem.radiusM + 2), // 10.0 (preserves exact radius)
            ),
            child: tileList,
          ),
        ),
      ],
    );
  }

  Widget buildTileList() {
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: tiles.length,
      padding: EdgeInsets.zero,
      itemBuilder: (BuildContext context, int index) {
        return tiles[index];
      },
    );
  }
}

class CustomSettingsSection extends AbstractSettingsSection {
  const CustomSettingsSection({
    required this.child,
    super.key,
  });

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return child;
  }
}
