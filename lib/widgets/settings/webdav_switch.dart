import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/enums/sync_direction.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/main.dart';
import 'package:dasso_reader/providers/anx_webdav.dart';
import 'package:dasso_reader/utils/webdav/show_status.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';
import 'package:dasso_reader/widgets/settings/settings_tile.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class WebDavSettings extends ConsumerWidget {
  const WebDavSettings({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final syncState = ref.watch(anxWebdavProvider);

    return ListTile(
      leading: const Icon(Icons.cached),
      title: Text(L10n.of(context).settings_sync_enable_webdav),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Show sync button when WebDAV is enabled
          if (Prefs().webdavStatus)
            syncState.isSyncing
                ? SemanticHelpers.button(
                    context: context,
                    child: IconButton(
                      icon: Icon(Icons.sync,
                          color: Theme.of(context).colorScheme.primary),
                      onPressed: () {
                        showWebdavStatus(context);
                      },
                    ),
                    label: 'Sync in progress',
                    hint: 'View sync status and progress',
                    onTap: () {
                      showWebdavStatus(context);
                    },
                  )
                : SemanticHelpers.button(
                    context: context,
                    child: IconButton(
                      icon: const Icon(Icons.sync),
                      onPressed: () {
                        ref
                            .read(anxWebdavProvider.notifier)
                            .syncData(SyncDirection.both, ref);
                      },
                    ),
                    label: 'Start sync',
                    hint: 'Synchronize data with WebDAV server',
                    onTap: () {
                      ref
                          .read(anxWebdavProvider.notifier)
                          .syncData(SyncDirection.both, ref);
                    },
                  ),
          Switch(
            value: Prefs().webdavStatus,
            onChanged: (bool value) async {
              Prefs().saveWebdavStatus(value);
              if (value) {
                bool result = await testEnableWebdav();
                if (!result) {
                  Prefs().saveWebdavStatus(!value);
                } else {
                  await AnxWebdav().init();
                  chooseDirection(ref);
                }
              }
            },
          ),
        ],
      ),
    );
  }
}

// For backward compatibility
AbstractSettingsTile webdavSwitch(
    BuildContext context, Function setState, WidgetRef ref) {
  return SettingsTile.switchTile(
    leading: const Icon(Icons.cached),
    initialValue: Prefs().webdavStatus,
    onToggle: (bool value) async {
      setState(() {
        Prefs().saveWebdavStatus(value);
      });
      if (value) {
        bool result = await testEnableWebdav();
        if (!result) {
          setState(() {
            Prefs().saveWebdavStatus(!value);
          });
        } else {
          AnxWebdav().init();
          chooseDirection(ref);
        }
      }
    },
    title: Text(L10n.of(context).settings_sync_enable_webdav),
  );
}

/// Test WebDAV connection and show configuration dialog
Future<bool> testEnableWebdav() async {
  if (Prefs().webdavInfo['url'] == null ||
      Prefs().webdavInfo['url'].toString().isEmpty) {
    // Show WebDAV configuration dialog if not configured
    final context = navigatorKey.currentContext;
    if (context != null) {
      await showWebdavDialog(context);
    }
    return Prefs().webdavInfo['url'] != null &&
        Prefs().webdavInfo['url'].toString().isNotEmpty;
  }

  try {
    // Test connection
    await AnxWebdav().init();
    return true;
  } catch (e) {
    // Connection failed, show error and configuration dialog
    final context = navigatorKey.currentContext;
    if (context != null) {
      await showWebdavDialog(context);
    }
    return false;
  }
}

/// Show sync direction selection dialog
void chooseDirection(WidgetRef ref) {
  final context = navigatorKey.currentContext;
  if (context == null) return;

  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: Text(L10n.of(context).settings_sync_webdav_sync_now),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.upload),
            title: Text(L10n.of(context).common_uploading),
            onTap: () {
              Navigator.pop(context);
              ref
                  .read(anxWebdavProvider.notifier)
                  .syncData(SyncDirection.upload, ref);
            },
          ),
          ListTile(
            leading: const Icon(Icons.download),
            title: Text(L10n.of(context).common_downloading),
            onTap: () {
              Navigator.pop(context);
              ref
                  .read(anxWebdavProvider.notifier)
                  .syncData(SyncDirection.download, ref);
            },
          ),
          ListTile(
            leading: const Icon(Icons.sync),
            title: const Text('Sync Both'),
            onTap: () {
              Navigator.pop(context);
              ref
                  .read(anxWebdavProvider.notifier)
                  .syncData(SyncDirection.both, ref);
            },
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(L10n.of(context).common_cancel),
        ),
      ],
    ),
  );
}

/// Show WebDAV configuration dialog
Future<void> showWebdavDialog(BuildContext context) async {
  final urlController =
      TextEditingController(text: Prefs().webdavInfo['url'] ?? '');
  final usernameController =
      TextEditingController(text: Prefs().webdavInfo['username'] ?? '');
  final passwordController =
      TextEditingController(text: Prefs().webdavInfo['password'] ?? '');

  return showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: Text(L10n.of(context).settings_sync_webdav),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: urlController,
            decoration: InputDecoration(
              labelText: 'WebDAV URL',
              hintText: 'https://example.com/webdav',
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: usernameController,
            decoration: const InputDecoration(
              labelText: 'Username',
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: passwordController,
            decoration: const InputDecoration(
              labelText: 'Password',
            ),
            obscureText: true,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(L10n.of(context).common_cancel),
        ),
        TextButton(
          onPressed: () {
            Prefs().saveWebdavInfo({
              'url': urlController.text,
              'username': usernameController.text,
              'password': passwordController.text,
            });
            Navigator.pop(context);
          },
          child: Text(L10n.of(context).common_save),
        ),
      ],
    ),
  );
}
