import 'package:flutter/material.dart';
import '../../config/design_system.dart';
import '../../config/responsive_system.dart';

/// Responsive layout widgets for Dasso Reader
///
/// Provides adaptive layouts that automatically adjust based on:
/// - Screen size and orientation
/// - Device type (phone, tablet, desktop)
/// - Content density preferences
/// - Accessibility requirements

// =====================================================
// RESPONSIVE CONTAINER
// =====================================================

/// A container that adapts its padding and constraints based on screen size
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final BoxConstraints? constraints;
  final Color? color;
  final Decoration? decoration;
  final double? width;
  final double? height;
  final AlignmentGeometry? alignment;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.padding,
    this.constraints,
    this.color,
    this.decoration,
    this.width,
    this.height,
    this.alignment,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? DesignSystem.getAdaptiveContentPadding(context),
      constraints: constraints,
      color: color,
      decoration: decoration,
      width: width,
      height: height,
      alignment: alignment,
      child: child,
    );
  }
}

// =====================================================
// RESPONSIVE GRID
// =====================================================

/// A grid that adapts its column count and spacing based on screen size
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final double? spacing;
  final double? runSpacing;
  final int? mobileColumns;
  final int? tabletColumns;
  final int? desktopColumns;
  final double? childAspectRatio;
  final ScrollPhysics? physics;
  final bool shrinkWrap;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.spacing,
    this.runSpacing,
    this.mobileColumns,
    this.tabletColumns,
    this.desktopColumns,
    this.childAspectRatio,
    this.physics,
    this.shrinkWrap = false,
  });

  @override
  Widget build(BuildContext context) {
    final columnCount = ResponsiveSystem.getAdaptiveColumnCount(
      context,
      mobilePortrait: mobileColumns ?? 2,
      mobileLandscape: (mobileColumns ?? 2) + 1,
      tabletPortrait: tabletColumns ?? 3,
      tabletLandscape: (tabletColumns ?? 3) + 1,
      desktopPortrait: desktopColumns ?? 4,
      desktopLandscape: (desktopColumns ?? 4) + 1,
    );

    final adaptiveSpacing =
        spacing ?? ResponsiveSystem.getAdaptiveGridSpacing(context);

    return GridView.count(
      crossAxisCount: columnCount,
      crossAxisSpacing: adaptiveSpacing,
      mainAxisSpacing: runSpacing ?? adaptiveSpacing,
      childAspectRatio: childAspectRatio ?? 1.0,
      physics: physics,
      shrinkWrap: shrinkWrap,
      children: children,
    );
  }
}

// =====================================================
// RESPONSIVE COLUMNS
// =====================================================

/// A widget that switches between Row and Column based on screen size
class ResponsiveColumns extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final double spacing;
  final double breakpoint;

  const ResponsiveColumns({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.spacing = 16.0,
    this.breakpoint = 600.0,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= breakpoint) {
          // Use Row for wider screens
          return Row(
            mainAxisAlignment: mainAxisAlignment,
            crossAxisAlignment: crossAxisAlignment,
            children: _addSpacing(children, isHorizontal: true),
          );
        } else {
          // Use Column for narrower screens
          return Column(
            mainAxisAlignment: mainAxisAlignment,
            crossAxisAlignment: crossAxisAlignment,
            children: _addSpacing(children, isHorizontal: false),
          );
        }
      },
    );
  }

  List<Widget> _addSpacing(List<Widget> widgets, {required bool isHorizontal}) {
    if (widgets.isEmpty) return widgets;

    final List<Widget> spacedWidgets = [];
    for (int i = 0; i < widgets.length; i++) {
      spacedWidgets.add(widgets[i]);
      if (i < widgets.length - 1) {
        spacedWidgets.add(
          isHorizontal ? SizedBox(width: spacing) : SizedBox(height: spacing),
        );
      }
    }
    return spacedWidgets;
  }
}

// =====================================================
// RESPONSIVE CARD
// =====================================================

/// A card that adapts its size and padding based on screen size
class ResponsiveCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? color;
  final double? elevation;
  final ShapeBorder? shape;
  final bool adaptiveElevation;

  const ResponsiveCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.color,
    this.elevation,
    this.shape,
    this.adaptiveElevation = true,
  });

  @override
  Widget build(BuildContext context) {
    final adaptivePadding =
        padding ?? DesignSystem.getAdaptiveContentPadding(context);
    final adaptiveMargin =
        margin ?? DesignSystem.getAdaptiveCardMargin(context);

    double cardElevation = elevation ?? 2.0;
    if (adaptiveElevation) {
      // Reduce elevation on mobile for better performance
      if (DesignSystem.isMobile(context) ||
          DesignSystem.isSmallPhone(context)) {
        cardElevation = cardElevation * 0.5;
      }
    }

    return Card(
      margin: adaptiveMargin,
      color: color,
      elevation: cardElevation,
      shape: shape,
      child: Padding(
        padding: adaptivePadding,
        child: child,
      ),
    );
  }
}

// =====================================================
// RESPONSIVE LIST TILE
// =====================================================

/// A list tile that adapts its height and padding based on screen size
class ResponsiveListTile extends StatelessWidget {
  final Widget? leading;
  final Widget? title;
  final Widget? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final bool dense;
  final EdgeInsetsGeometry? contentPadding;

  const ResponsiveListTile({
    super.key,
    this.leading,
    this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.dense = false,
    this.contentPadding,
  });

  @override
  Widget build(BuildContext context) {
    final adaptivePadding =
        contentPadding ?? DesignSystem.getAdaptiveListItemPadding(context);
    final minHeight = ResponsiveSystem.getAdaptiveListItemHeight(context);

    return ConstrainedBox(
      constraints: BoxConstraints(minHeight: minHeight),
      child: ListTile(
        leading: leading,
        title: title,
        subtitle: subtitle,
        trailing: trailing,
        onTap: onTap,
        dense: dense,
        contentPadding: adaptivePadding,
      ),
    );
  }
}

// =====================================================
// RESPONSIVE BUTTON
// =====================================================

/// A button that adapts its size and padding based on screen size
class ResponsiveButton extends StatelessWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final ButtonStyle? style;
  final bool isCompact;

  const ResponsiveButton({
    super.key,
    required this.child,
    this.onPressed,
    this.style,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final adaptivePadding = isCompact
        ? DesignSystem.compactButtonPadding
        : DesignSystem.getAdaptiveButtonPadding(context);

    return ConstrainedBox(
      constraints: ResponsiveSystem.getTouchTargetConstraints(context),
      child: ElevatedButton(
        onPressed: onPressed,
        style: style?.copyWith(
              padding: WidgetStateProperty.all(adaptivePadding),
            ) ??
            ElevatedButton.styleFrom(
              padding: adaptivePadding,
            ),
        child: child,
      ),
    );
  }
}

// =====================================================
// RESPONSIVE TEXT
// =====================================================

/// Text that adapts its overflow and max lines based on screen size
///
/// Note: This widget is now a wrapper around DynamicTextWidget for backward compatibility.
/// For new implementations, consider using DynamicTextWidget directly.
class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final int? maxLines;
  final TextOverflow? overflow;
  final TextAlign? textAlign;

  const ResponsiveText(
    this.text, {
    super.key,
    this.style,
    this.maxLines,
    this.overflow,
    this.textAlign,
  });

  @override
  Widget build(BuildContext context) {
    final adaptiveMaxLines =
        maxLines ?? ResponsiveSystem.getAdaptiveMaxLines(context);
    final adaptiveOverflow =
        overflow ?? ResponsiveSystem.getAdaptiveTextOverflow(context);

    return Text(
      text,
      style: style,
      maxLines: adaptiveMaxLines,
      overflow: adaptiveOverflow,
      textAlign: textAlign,
    );
  }
}
