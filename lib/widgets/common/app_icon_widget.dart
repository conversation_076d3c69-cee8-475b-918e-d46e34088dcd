import 'package:flutter/material.dart';
import 'package:dasso_reader/config/app_icons.dart';
import 'package:dasso_reader/config/color_system.dart';
import 'package:dasso_reader/config/design_system.dart';

/// A comprehensive icon widget that provides consistent styling and behavior
/// across the Dasso Reader app. This widget ensures all icons follow the
/// established design system and accessibility guidelines.
class AppIconWidget extends StatelessWidget {
  /// The icon data to display
  final IconData icon;

  /// The size of the icon
  final IconSize size;

  /// The type/context of the icon for appropriate coloring
  final IconType type;

  /// Custom color override (optional)
  final Color? color;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to use adaptive sizing based on screen size
  final bool isAdaptive;

  /// Whether the icon represents an active/selected state
  final bool isActive;

  /// Custom opacity for the icon
  final double? opacity;

  const AppIconWidget(
    this.icon, {
    super.key,
    this.size = IconSize.m,
    this.type = IconType.primary,
    this.color,
    this.semanticsLabel,
    this.isAdaptive = true,
    this.isActive = false,
    this.opacity,
  });

  @override
  Widget build(BuildContext context) {
    final iconSize =
        AppIcons.getResponsiveSize(context, size, isAdaptive: isAdaptive);
    final iconColor = _getIconColor(context);

    return Icon(
      icon,
      size: iconSize,
      color:
          opacity != null ? iconColor.withValues(alpha: opacity!) : iconColor,
      semanticLabel: semanticsLabel,
    );
  }

  /// Get the appropriate color for the icon based on type and state
  Color _getIconColor(BuildContext context) {
    if (color != null) return color!;

    // Adjust color based on active state
    if (isActive && type == IconType.primary) {
      return Theme.of(context).colorScheme.primary;
    }

    return ColorSystem.getIconColor(context, type);
  }
}

/// A comprehensive icon button widget that provides consistent styling,
/// behavior, and accessibility features across the Dasso Reader app.
class AppIconButton extends StatelessWidget {
  /// The icon data to display
  final IconData icon;

  /// Callback when the button is pressed
  final VoidCallback? onPressed;

  /// The size of the icon
  final IconSize size;

  /// The type/context of the icon for appropriate coloring
  final IconType type;

  /// Custom color override (optional)
  final Color? color;

  /// Tooltip text for accessibility and user guidance
  final String? tooltip;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to use adaptive sizing based on screen size
  final bool isAdaptive;

  /// Whether the icon represents an active/selected state
  final bool isActive;

  /// Custom padding around the icon
  final EdgeInsetsGeometry? padding;

  /// Custom constraints for the button
  final BoxConstraints? constraints;

  /// Whether to show a visual indicator for the active state
  final bool showActiveIndicator;

  const AppIconButton(
    this.icon, {
    super.key,
    required this.onPressed,
    this.size = IconSize.m,
    this.type = IconType.primary,
    this.color,
    this.tooltip,
    this.semanticsLabel,
    this.isAdaptive = true,
    this.isActive = false,
    this.padding,
    this.constraints,
    this.showActiveIndicator = false,
  });

  @override
  Widget build(BuildContext context) {
    final iconSize =
        AppIcons.getResponsiveSize(context, size, isAdaptive: isAdaptive);
    final iconColor = _getIconColor(context);

    // Calculate minimum touch target size for accessibility
    final minTouchTarget = _getMinimumTouchTarget(iconSize);

    Widget iconButton = IconButton(
      onPressed: onPressed,
      icon: AppIconWidget(
        icon,
        size: size,
        type: type,
        color: iconColor,
        semanticsLabel: semanticsLabel,
        isAdaptive: isAdaptive,
        isActive: isActive,
      ),
      tooltip: tooltip,
      padding: padding ?? EdgeInsets.all(DesignSystem.spaceS),
      constraints: constraints ??
          BoxConstraints(
            minWidth: minTouchTarget,
            minHeight: minTouchTarget,
          ),
    );

    // Add active state indicator if requested
    if (showActiveIndicator && isActive) {
      iconButton = Stack(
        alignment: Alignment.center,
        children: [
          // Active state background
          Container(
            width: iconSize + 16,
            height: iconSize + 16,
            decoration: BoxDecoration(
              color:
                  Theme.of(context).colorScheme.primary.withValues(alpha: 0.12),
              borderRadius: BorderRadius.circular(DesignSystem.radiusS),
            ),
          ),
          iconButton,
        ],
      );
    }

    return iconButton;
  }

  /// Get the appropriate color for the icon based on type and state
  Color _getIconColor(BuildContext context) {
    if (color != null) return color!;

    // Adjust color based on active state
    if (isActive && type == IconType.primary) {
      return Theme.of(context).colorScheme.primary;
    }

    return ColorSystem.getIconColor(context, type);
  }

  /// Calculate minimum touch target size for accessibility compliance
  double _getMinimumTouchTarget(double iconSize) {
    // Ensure minimum 44dp touch target as per accessibility guidelines
    const minAccessibleSize = 44.0;
    return iconSize < minAccessibleSize ? minAccessibleSize : iconSize + 20;
  }
}

/// A circular icon button widget for special contexts like floating action
/// buttons or context menu actions.
class AppCircleIconButton extends StatelessWidget {
  /// The icon data to display
  final IconData icon;

  /// Callback when the button is pressed
  final VoidCallback? onPressed;

  /// The size of the icon
  final IconSize size;

  /// The type/context of the icon for appropriate coloring
  final IconType type;

  /// Custom color override (optional)
  final Color? color;

  /// Background color for the circle
  final Color? backgroundColor;

  /// Tooltip text for accessibility and user guidance
  final String? tooltip;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to use adaptive sizing based on screen size
  final bool isAdaptive;

  /// Whether to show elevation/shadow
  final bool elevated;

  const AppCircleIconButton(
    this.icon, {
    super.key,
    required this.onPressed,
    this.size = IconSize.m,
    this.type = IconType.primary,
    this.color,
    this.backgroundColor,
    this.tooltip,
    this.semanticsLabel,
    this.isAdaptive = true,
    this.elevated = false,
  });

  @override
  Widget build(BuildContext context) {
    final iconSize =
        AppIcons.getResponsiveSize(context, size, isAdaptive: isAdaptive);
    final iconColor = color ?? ColorSystem.getIconColor(context, type);
    final bgColor = backgroundColor ?? Theme.of(context).colorScheme.surface;

    // Calculate circle size based on icon size
    final circleSize = iconSize + 24;

    return Tooltip(
      message: tooltip ?? '',
      child: Material(
        color: bgColor,
        elevation: elevated ? DesignSystem.elevationS : 0,
        shape: const CircleBorder(),
        child: InkWell(
          onTap: onPressed,
          customBorder: const CircleBorder(),
          child: Container(
            width: circleSize,
            height: circleSize,
            alignment: Alignment.center,
            child: AppIconWidget(
              icon,
              size: size,
              type: type,
              color: iconColor,
              semanticsLabel: semanticsLabel,
              isAdaptive: isAdaptive,
            ),
          ),
        ),
      ),
    );
  }
}

/// A specialized icon widget for navigation contexts that automatically
/// handles active states and provides consistent navigation styling.
class AppNavigationIcon extends StatelessWidget {
  /// The icon data to display
  final IconData icon;

  /// The filled version of the icon for active state
  final IconData? iconFilled;

  /// Whether this navigation item is currently active
  final bool isActive;

  /// The size of the icon
  final IconSize size;

  /// Custom color override (optional)
  final Color? color;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  const AppNavigationIcon(
    this.icon, {
    super.key,
    this.iconFilled,
    this.isActive = false,
    this.size = IconSize.m,
    this.color,
    this.semanticsLabel,
  });

  @override
  Widget build(BuildContext context) {
    // Use filled icon for active state if available
    final displayIcon = isActive && iconFilled != null ? iconFilled! : icon;

    return AppIconWidget(
      displayIcon,
      size: size,
      type: isActive ? IconType.primary : IconType.secondary,
      color: color,
      semanticsLabel: semanticsLabel,
      isActive: isActive,
    );
  }
}
