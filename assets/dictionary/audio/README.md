# Dictionary Audio

This directory is reserved for audio files related to the dictionary functionality.

## Online TTS Services

Instead of storing audio files locally, the app uses online Text-to-Speech (TTS) services to provide pronunciation for Chinese characters and words. This approach:

1. Eliminates the need to store large audio files locally
2. Provides better coverage for all Chinese characters
3. Reduces app size and storage requirements

## Implementation

The app uses the following online TTS services:

1. Primary: TTS Maker API
2. Fallback: Google Translate TTS API

If you need to add local audio files for specific use cases, you can place them in this directory with the following naming convention:

- Single character: `[character].mp3` (e.g., `你.mp3`)
- Words: `[word].mp3` (e.g., `你好.mp3`)

However, the default behavior is to use online TTS services.

## Directory Structure

- `audio/` - Root directory for all audio files
  - `[character].mp3`