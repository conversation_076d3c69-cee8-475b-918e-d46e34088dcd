name: dasso_reader
description: "A customized e-book reader focused on Chinese language learning, maintained by dassodev."
publish_to: "none"

version: 1.0.0+1

environment:
  sdk: ">=3.5.2 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  file_picker: ^9.0.2
  path_provider: ^2.1.2
  image: ^4.3.0
  sqflite: ^2.3.2
  path: ^1.9.0
  shared_preferences: ^2.2.2
  provider: ^6.1.2
  intl: ^0.19.0
  flutter_colorpicker: ^1.0.3
  html: ^0.15.4
  archive: ^3.6.1
  flutter_inappwebview:
    git:
      url: https://github.com/Anxcye/flutter_inappwebview.git
      path: flutter_inappwebview
  shelf: ^1.4.1
  fl_chart: ^0.70.2
  flutter_rating_bar: ^4.0.1
  permission_handler: ^11.3.1
  webdav_client:
    git:
      url: https://github.com/Anxcye/webdav_client.git
  fluttertoast: ^8.2.6
  dio: ^5.4.3+1
  pubspec_parse: ^1.2.3
  logging: ^1.2.0
  flutter_file_dialog: ^3.0.2
  contentsize_tabbarview:
    git:
      url: https://github.com/Anxcye/contentsize_tabbarview.git
      ref: feat/animation
  wakelock_plus: ^1.2.5
  icons_plus: ^5.0.0
  url_launcher: ^6.2.6
  sqflite_common_ffi: ^2.3.3
  battery_plus: ^6.2.1
  flutter_tts: ^4.2.2
  sticky_headers: ^0.3.0+2
  photo_view: ^0.15.0
  flutter_smart_dialog: ^4.9.8+1
  saver_gallery: ^4.0.1
  share_plus: ^10.0.2
  device_info_plus: ^11.3.2
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5
  chinese_font_library: ^1.2.0
  flex_color_scheme: ^8.2.0
  flutter_reorderable_grid_view: ^5.4.0
  uuid: ^4.5.1
  receive_sharing_intent: ^1.8.1
  audio_service: ^0.18.16
  audio_session: ^0.1.23
  freezed_annotation: ^3.0.0
  flutter_heatmap_calendar:
    git:
      url: https://github.com/Anxcye/flutter_heatmap_calendar
  csv: ^6.0.0
  fast_gbk: ^1.0.0
  flutter_markdown: ^0.7.6+2
  pointer_interceptor: ^0.10.1+2
  connectivity_plus: ^6.1.3
  http: ^1.3.0
  web_socket_channel: ^3.0.2
  crypto: ^3.0.6
  audioplayers: ^6.4.0
  in_app_purchase: ^3.2.1
  asn1lib: ^1.6.2
  in_app_purchase_storekit: ^0.3.21
  charset: ^2.0.1
  flutter_slidable: ^4.0.0
  cached_network_image: ^3.4.1
  just_audio: ^0.9.36
  pinyin: ^3.2.0
  characters: ^1.3.0
  hive: ^2.2.3
  stroke_order_animator: ^3.3.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  test: any
  build_runner: ^2.4.13
  custom_lint: ^0.7.5
  riverpod_generator: ^2.4.3
  riverpod_lint: ^2.3.13
  freezed: ^3.0.2
  json_serializable: ^6.9.0
  assets_cleaner: ^0.1.5+12

flutter:
  uses-material-design: true
  generate: true
  assets:
    - assets/fonts/NotoSansSC-Regular.ttf
    - assets/fonts/NotoSansSC-Bold.ttf
    - pubspec.yaml
    - assets/foliate-js/
    - assets/foliate-js/vendor/
    - assets/foliate-js/vendor/pdfjs/
    - assets/images/openai.png
    - assets/images/claude.png
    - assets/images/deepseek.png
    - assets/images/gemini.png
    - assets/images/commonAi.png
    - assets/images/qq.png
    - assets/images/xiaohongshu.png
    - assets/dict/chinese_dict.txt
    - assets/dictionary/chinese_dictionary.db
    - assets/audio/hsk1/
    - assets/audio/hsk2/
    - assets/audio/hsk3/
    - assets/audio/hsk4/
    - assets/audio/hsk5/
    - assets/audio/hsk6/
    - assets/hsk-json-all/hsk-level-1.json
    - assets/hsk-json-all/hsk-level-2.json
    - assets/hsk-json-all/hsk-level-3.json
    - assets/hsk-json-all/hsk-level-4.json
    - assets/hsk-json-all/hsk-level-5.json
    - assets/hsk-json-all/hsk-level-6.json

  fonts:
    - family: NotoSansSC
      fonts:
        - asset: assets/fonts/NotoSansSC-Regular.ttf
          weight: 400
        - asset: assets/fonts/NotoSansSC-Bold.ttf
          weight: 700